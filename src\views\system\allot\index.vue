<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="调拨单号" prop="allotNo">
          <el-input
            v-model="queryParams.allotNo"
            placeholder="请输入调拨单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="经手人" prop="handledBy">
        <el-input
          v-model="queryParams.handledBy"
          placeholder="请输入经手人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调出仓库id" prop="outWarehouseId">
        <el-input
          v-model="queryParams.outWarehouseId"
          placeholder="请输入调出仓库id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调出仓库编码" prop="outWarehouseCode">
        <el-input
          v-model="queryParams.outWarehouseCode"
          placeholder="请输入调出仓库编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调出仓库名称" prop="outWarehouseName">
        <el-input
          v-model="queryParams.outWarehouseName"
          placeholder="请输入调出仓库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调入仓库id" prop="inWarehouseId">
        <el-input
          v-model="queryParams.inWarehouseId"
          placeholder="请输入调入仓库id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调入仓库编码" prop="inWarehouseCode">
        <el-input
          v-model="queryParams.inWarehouseCode"
          placeholder="请输入调入仓库编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调入仓库名称" prop="inWarehouseName">
        <el-input
          v-model="queryParams.inWarehouseName"
          placeholder="请输入调入仓库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item label="调拨状态" prop="allotState">
          <el-select
            v-model="queryParams.allotState"
            placeholder="请选择调拨状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.allot_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="调拨方式--字典allot_method" prop="allotMethod">
        <el-select v-model="queryParams.allotMethod" placeholder="请选择调拨方式--字典allot_method" clearable>
          <el-option
            v-for="dict in dict.type.allot_method"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

        <!-- <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:allot:add']"
            >新增</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:allot:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:allot:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:allot:export']"
            >导出</el-button
          >
        </el-col> -->
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="allotList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" width="50" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
        <el-table-column
          label="调拨单号"
          align="center"
          prop="allotNo"
          :width="tableWidth(allotList.map((x) => x.allotNo))"
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.allotNo"
              >
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.allotNo
                }}</span>
              </el-tooltip>
              <i
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.allotNo"
                v-clipboard:success="onCopy"
              ></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="调拨日期"
          align="center"
          prop="allotDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.allotDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="经手人" align="center" prop="handledBy" />
        <el-table-column
          label="调出仓库名称"
          align="center"
          prop="outWarehouseName"
          :width="tableWidth(allotList.map((x) => x.outWarehouseName))"
        />
        <!-- <el-table-column
          label="调出仓库id"
          align="center"
          prop="outWarehouseId"
        /> -->
        <!-- <el-table-column
          label="调出仓库编码"
          align="center"
          prop="outWarehouseCode"
        /> -->

        <!-- <el-table-column
          label="调入仓库id"
          align="center"
          prop="inWarehouseId"
        /> -->
        <!-- <el-table-column
          label="调入仓库编码"
          align="center"
          prop="inWarehouseCode"
        /> -->
        <el-table-column
          label="调入仓库名称"
          align="center"
          prop="inWarehouseName"
          :width="tableWidth(allotList.map((x) => x.inWarehouseName))"
        />
        <el-table-column
          label="调拨方式"
          align="center"
          prop="allotMethod"
          :width="tableWidth(allotList.map((x) => x.allotMethod))"
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.allot_method"
              :value="scope.row.allotMethod"
            />
          </template>
        </el-table-column>
        <el-table-column label="调拨状态" align="center" prop="allotState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.allot_state"
              :value="scope.row.allotState"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="调拨日期"
          align="center"
          prop="allotDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.allotDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="调出仓库标识"
          align="center"
          prop="outWareType"
          :width="tableWidth(allotList.map((x) => x.outWareType))"
        />
        <el-table-column
          label="调入仓库标识"
          align="center"
          prop="inWareType"
          :width="tableWidth(allotList.map((x) => x.inWareType))"
        />
        <el-table-column label="创建人" align="center" prop="createBy" />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="修改人" align="center" prop="updateBy" />
        <el-table-column
          label="修改时间"
          align="center"
          prop="updateTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" /> -->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          :width="tableWidth(allotList.map((x) => x.inWareType))"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:allot:edit']"
              v-show="scope.row.allotState == 'CREATED'"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:allot:remove']"
              v-show="scope.row.allotState == 'CREATED'"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleDatail(scope.row)"
              v-hasPermi="['system:allot:edit']"
              v-show="scope.row.allotState == 'ENTRY_COMPLETED'"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handlecomplete(scope.row)"
              v-hasPermi="['system:allot:edit']"
              v-show="scope.row.allotState == 'CREATED'"
            >
              录入完成
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改调拨管理对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'55%'">
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="调拨单信息" name="1">
              <!-- <el-form-item label="调拨单号" prop="allotNo" style="width: 240px">
            <el-input v-model="form.allotNo" placeholder="请输入调拨单号" />
          </el-form-item> -->

              <el-row class="mb8">
                <el-col :span="12">
                  <el-form-item
                    label="调拨方式"
                    prop="allotMethod"
                    style="width: 240px"
                  >
                    <el-select
                      v-model="form.allotMethod"
                      placeholder="请选择调拨方式"
                      style="width: 240px"
                    >
                      <el-option
                        v-for="dict in dict.type.allot_method"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="经手人"
                    prop="handledBy"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.handledBy"
                      placeholder="请输入经手人"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="调拨状态"
                    prop="allotState"
                    style="width: 240px"
                  >
                    <el-select
                      disabled
                      v-model="form.allotState"
                      placeholder="请选择调拨状态"
                      style="width: 240px"
                    >
                      <el-option
                        v-for="dict in dict.type.allot_state"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="调出仓库名称"
                    prop="outWarehouseName"
                    style="width: 240px"
                  >
                    <el-input
                      @focus="importFuction('out')"
                      placeholder="请输入调调出仓库名称"
                      v-model="form.outWarehouseName"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="调入仓库名称"
                    prop="inWarehouseName"
                    style="width: 240px"
                  >
                    <el-input
                      @focus="importFuction('in')"
                      placeholder="请输入调入仓库名称"
                      v-model="form.inWarehouseName"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="调拨日期"
                    prop="allotDate"
                    style="width: 240px"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.allotDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择调拨日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注" prop="remark" style="width: 700px">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      placeholder="请输入内容"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-form-item
            label="调出仓库id"
            prop="outWarehouseId"
            style="width: 240px"
          >
            <el-input
              v-model="form.outWarehouseId"
              placeholder="请输入调出仓库id"
            />
          </el-form-item> -->
              <!-- <el-form-item
            label="调出仓库编码"
            prop="outWarehouseCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.outWarehouseCode"
              placeholder="请输入调出仓库编码"
            />
          </el-form-item> -->

              <!-- <el-form-item
            label="调入仓库id"
            prop="inWarehouseId"
            style="width: 240px"
          >
            <el-input
              v-model="form.inWarehouseId"
              placeholder="请输入调入仓库id"
            />
          </el-form-item> -->
              <!-- <el-form-item
            label="调入仓库编码"
            prop="inWarehouseCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.inWarehouseCode"
              placeholder="请输入调入仓库编码"
            />
          </el-form-item> -->
              <!-- <el-form-item
            label="调入仓库名称"
            prop="inWarehouseName"
            style="width: 240px"
          >
            <el-input
              v-model="form.inWarehouseName"
              placeholder="请输入调入仓库名称"
            />
          </el-form-item>
          <el-form-item label="调拨状态" prop="allotState" style="width: 240px">
            <el-select
              v-model="form.allotState"
              placeholder="请选择调拨状态"
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.allot_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="调拨日期" prop="allotDate" style="width: 240px">
            <el-date-picker
              clearable
              v-model="form.allotDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择调拨日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="备注" prop="remark" style="width: 700px">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item> -->
            </el-collapse-item>
            <el-collapse-item title="调拨明细单信息" name="2">
              <el-row :gutter="10" class="mb8" style="margin-left: 5px">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAddWmsAllotDetail"
                    >添加</el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="handleDeleteWmsAllotDetail"
                    >删除</el-button
                  >
                </el-col>
              </el-row>
              <el-table
                :data="wmsAllotDetailList"
                :row-class-name="rowWmsAllotDetailIndex"
                @selection-change="handleWmsAllotDetailSelectionChange"
                ref="wmsAllotDetail"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column
                  label="序号"
                  align="center"
                  prop="index"
                  width="50"
                />
                <!-- <el-table-column label="调拨单号" prop="allotNo" width="150">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.allotNo"
                  placeholder="请输入调拨单号"
                />
              </template>
            </el-table-column> -->
                <!-- <el-table-column label="物料id" prop="materialId" width="150">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.materialId"
                  placeholder="请输入物料id"
                />
              </template>
            </el-table-column> -->
                <el-table-column
                  label="物料编码"
                  prop="materialCode"
                  width="150"
                >
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.materialCode"
                      placeholder="请输入物料编码"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="物料名称"
                  prop="materialName"
                  width="150"
                >
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.materialName"
                      placeholder="请输入物料名称"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="规格型号"
                  prop="specification"
                  width="150"
                >
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.specification"
                      placeholder="请输入规格型号"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="单位" prop="materialUnit" width="150">
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.materialUnit"
                      placeholder="请输入单位"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="qty">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.qty"
                      placeholder="请输入数量"
                    />
                  </template>
                </el-table-column>
                <!-- <el-table-column label="已调拨数量" prop="incomingQty" width="150">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.incomingQty"
                  placeholder="请输入已调拨数量"
                />
              </template>
            </el-table-column> -->
                <!-- <el-table-column label="批次" prop="batchNo" width="150">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.batchNo"
                  placeholder="请输入批次"
                />
              </template>
            </el-table-column> -->
                <!-- <el-table-column label="调拨状态" prop="allotState" width="150">
              <el-select
                v-model="form.allotState"
                placeholder="请选择调拨状态"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.allot_state"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-table-column> -->
                <!-- <el-table-column label="组织" prop="comId" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.comId" placeholder="请输入组织" />
              </template>
            </el-table-column> -->
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-form>

        <div class="drawer_submit">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 详情对话框-->
      <el-drawer
        :title="detailTitle"
        :visible.sync="openDetail"
        :size="'75%'"
        append-to-body
      >
        <el-form
          ref="form"
          :model="form"
          size="small"
          :inline="true"
          label-width="100px"
        >
          <!-- <el-date-picker
              clearable
              v-model="form.stockOutDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择出库日期"
            >
            </el-date-picker> -->
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="调拨单信息" name="1">
              <el-form-item
                label="调拨单号"
                prop="allotNo"
                style="width: 340px"
              >
                <el-input disabled v-model="form.allotNo" placeholder="" />
              </el-form-item>
              <el-form-item
                label="调拨状态"
                prop="allotState"
                style="width: 340px"
              >
                <el-select
                  disabled
                  v-model="form.allotState"
                  placeholder="请选择调拨状态"
                  style="width: 240px"
                >
                  <!-- <el-option
                v-for="dict in dict.type.allot_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option> -->
                </el-select>
              </el-form-item>
              <el-form-item label="调拨日期" prop="allotDate">
                <el-input disabled v-model="form.allotDate" placeholder="" />
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="调拨明细单信息" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane
                  label="入库明细"
                  name="first"
                  :data="form.wmsAllotDetailList"
                >
                  <!-- <Outdetail
                :allot_id="allot_id"
                :key="allot_id"
                :activeName="activeName"
              /> -->
                  <AllotDetail
                    :allot_id="allot_id"
                    :key="allot_id"
                    :activeName="activeName"
                  />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <AllotDetail
                        @sendDetailId="receiveDetailData"
                        :allot_id="allot_id"
                        :key="allot_id"
                        :activeName="activeName"
                        :tableData="form.wmsAllotDetailList"
                      />
                    </div>
                    <div style="width: 48%">
                      <AllotBox
                        :allot_detail_id="allot_detail_id"
                        :key="allot_detail_id"
                      />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
      <!-- 明细添加调出调入仓库对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="warehouseDialogVisible"
        width="60%"
        :before-close="handleWarehouseClose"
      >
        <el-form :data="warehouseList" :inline="true" label-width="100px">
          <el-table
            ref="warehouseTable"
            :data="warehouseList"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChangeWarehouse"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column
              label="仓库名称"
              align="center"
              prop="warehouseName"
            />
            <el-table-column
              label="仓库编码"
              align="center"
              prop="warehouseCode"
            />
            <el-table-column
              label="仓库类型"
              align="center"
              prop="warehouseType"
            />
            <el-table-column label="仓库标识" align="center" prop="wareSign" />
          </el-table>
        </el-form>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadListMaterials"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleWarehouseClose">取 消</el-button>
          <el-button type="primary" @click="handleAddWarehouse"
            >确 定</el-button
          >
        </span>
      </el-dialog>
      <!-- 明细添加物料对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >
        <el-form :data="materialList" :inline="true" label-width="100px">
          <el-table
            ref="materialTable"
            :data="materialList"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChangematerial"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column
              label="物料编码"
              align="center"
              prop="materialCode"
            />
            <el-table-column
              label="物料名称"
              align="center"
              prop="materialName"
            />
            <el-table-column
              label="规格型号"
              align="center"
              prop="specification"
            />
            <el-table-column label="单位" align="center" prop="materialUnit" />
          </el-table>
        </el-form>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadListMaterials"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleAddMaterial">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listAllot,
  getAllot,
  delAllot,
  addAllot,
  updateAllot,
} from "@/api/system/allot";
import AllotDetail from "@/views/system/allotDetail/allotDetail.vue";
import AllotBox from "@/views/system/allotBox/allotBox.vue";
import { listMaterial } from "@/api/system/material";
import { listWarehouse } from "@/api/system/warehouse";
// import Outdetail from "@/views/stockOut/stockOutDetail/outdetail.vue";
// import OutBox from "@/views/stockOut/stockOutBox/OutBox.vue";

export default {
  name: "Allot",
  dicts: ["allot_state", "allot_method"],
  components: {
    AllotBox,
    AllotDetail, // 注册子组件
  },
  data() {
    return {
      // 新增临时存储变量
      tempSelectedWarehouse: null,
      // 调入或调出
      type: "",
      // 仓库list
      warehouseList: [],
      activeNames: ["1", "2"],
      activeNamesInfo: ["1", "2"],
      // 控制选择仓库对话框
      warehouseDialogVisible: false,
      // 控制明细添加物料对话框
      dialogVisible: false,
      // 物料表格数据
      materialList: [],
      // 选中的物料数据
      detailmaterialList: [],
      // 调出仓库
      warehouseOut: { warehouseName: "", warehouseCode: "", warehouseType: "" },
      // 调入仓库
      warehouseIn: { warehouseName: "", warehouseCode: "", warehouseType: "" },
      isCompleted: false,
      detailTitle: "",
      openDetail: false,
      activeName: "first",
      allot_id: "",
      allot_detail_id: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsAllotDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调拨管理表格数据
      allotList: [],
      // 调拨单明细表格数据
      wmsAllotDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //调拨数组
      allotNos: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        allotNo: null,
        handledBy: null,
        outWarehouseId: null,
        outWarehouseCode: null,
        outWarehouseName: null,
        inWarehouseId: null,
        inWarehouseCode: null,
        inWarehouseName: null,
        allotState: null,
        allotMethod: null,
        allotDate: null,
        outWareType: null,
        inWareType: null,
        comId: null,
      },
      // defaultAllotState: "CREATED",

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
        allotMethod: [
          { required: true, message: "调拨方式不能为空", trigger: "blur" },
        ],
        handledBy: [
          { required: true, message: "经手人不能为空", trigger: "blur" },
        ],
        // allotState: [
        //   { required: true, message: "删除标志不能为空", trigger: "blur" },
        // ],
        outWarehouseName: [
          { required: true, message: "调出仓库不能为空", trigger: "blur" },
        ],
        inWarehouseName: [
          { required: true, message: "调入仓库不能为空", trigger: "blur" },
        ],
        allotDate: [
          { required: true, message: "调拨日期不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.loadListMaterials();
    this.loadListWarehouse();
  },
  methods: {
    /** 查询调拨管理列表 */
    getList() {
      this.loading = true;
      listAllot(this.queryParams).then((response) => {
        // this.allotList = this.sortArrayByField(response.rows, "createTime");
        this.allotList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        allotNo: null,
        handledBy: null,
        outWarehouseId: null,
        outWarehouseCode: null,
        outWarehouseName: null,
        inWarehouseId: null,
        inWarehouseCode: null,
        inWarehouseName: null,
        allotState: "CREATED",
        allotMethod: null,
        allotDate: null,
        outWareType: null,
        inWareType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.wmsAllotDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleSelectionChangematerial(selection) {
      this.ids = selection.map((item) => item.id);
      console.log("选中的ID列表：", this.ids);
      // 获取选中项的ID数组

      // 根据ID筛选需要提交的数据
      this.detailmaterialList = this.materialList.filter((item) =>
        this.ids.includes(item.id)
      );
      console.log("筛选后的提交数据：", this.detailmaterialList);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleSelectionChangeWarehouse(selection) {
      if (selection.length > 1) {
        // 只保留最后一次选择的行
        const lastSelected = selection[selection.length - 1];
        this.$refs.warehouseTable.clearSelection();
        this.$refs.warehouseTable.toggleRowSelection(lastSelected, true);
        selection = [lastSelected];
      }

      if (selection.length > 1) {
        const lastSelected = selection[selection.length - 1];
        this.$refs.warehouseTable.clearSelection();
        this.$refs.warehouseTable.toggleRowSelection(lastSelected, true);
        selection = [lastSelected];
      }

      // 将选中的仓库对象暂存到临时变量
      if (this.type === "out") {
        this.tempSelectedWarehouse = selection[0] || null;
      } else if (this.type === "in") {
        this.tempSelectedWarehouse = selection[0] || null;
      }

      console.log("当前暂存的仓库：", this.tempSelectedWarehouse);

      // this.ids = selection.map((item) => item.id);
      // console.log("选中的ID列表：", this.ids);
      // console.log("仓库调：", this.type);
      // // 获取选中项的ID数组
      // if (this.type == "out") {
      //   console.log("仓库调出：", selection[0]);
      //   this.form.outWarehouseId = selection[0].id;
      //   this.form.outWarehouseCode = selection[0].warehouseCode;
      //   this.form.outWarehouseName = selection[0].warehouseName;
      //   this.form.outWareType = selection[0].warehouseType;
      //   // outWarehouseId: null,
      //   // outWarehouseCode: null,
      //   // outWarehouseName: null,
      //   // outWareType: null,

      //   // 这里去把值copy到warehouseOut，而不是赋值引用
      //   // this.form = { ...selection[0] };
      //   // console.log("仓库调出：", this.warehouseOut);
      //   // this.warehouseIn = selection[0];
      //   // console.log("仓库调入：", this.warehouseIn);
      //   console.log("仓库调出：", this.form);
      // }
      // if (this.type == "in") {
      //   console.log("仓库调入：", selection[0]);
      //   this.form.inWarehouseId = selection[0].id;
      //   this.form.inWarehouseCode = selection[0].warehouseCode;
      //   this.form.inWarehouseName = selection[0].warehouseName;
      //   this.form.inWareType = selection[0].warehouseType;

      //   // this.form = selection[0];
      //   // console.log("仓库调入：", this.warehouseIn);
      //   // this.warehouseIn = { ...selection[0] };
      //   // console.log("仓库调入：", this.warehouseIn);
      //   console.log("仓库调入：", this.form);
      // }

      // this.single = selection.length !== 1;
      // this.multiple = !selection.length;
    },
    handleAddWarehouse() {
      if (!this.tempSelectedWarehouse) {
        this.$modal.msgError("请先选择仓库");
        return;
      }

      // 根据类型赋值到对应字段
      if (this.type === "out") {
        this.form.outWarehouseId = this.tempSelectedWarehouse.id;
        this.form.outWarehouseCode = this.tempSelectedWarehouse.warehouseCode;
        this.form.outWarehouseName = this.tempSelectedWarehouse.warehouseName;
        this.form.outWareType = this.tempSelectedWarehouse.warehouseType;
      } else if (this.type === "in") {
        this.form.inWarehouseId = this.tempSelectedWarehouse.id;
        this.form.inWarehouseCode = this.tempSelectedWarehouse.warehouseCode;
        this.form.inWarehouseName = this.tempSelectedWarehouse.warehouseName;
        this.form.inWareType = this.tempSelectedWarehouse.warehouseType;
      }

      // 清空临时存储
      this.tempSelectedWarehouse = null;
      this.warehouseDialogVisible = false;
      this.$refs.warehouseTable.clearSelection();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调拨仓库";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAllot(id).then((response) => {
        this.form = response.data;
        this.wmsAllotDetailList = response.data.wmsAllotDetailList;
        this.open = true;
        this.title = "修改调拨仓库";
      });
    },
    // 录入完成
    handlecomplete(row) {
      this.$modal
        .confirm('是否确认录入编号为"' + row.allotNo + '"的数据项？')
        .then(function () {})
        .then(() => {
          getAllot(row.id).then((response) => {
            this.form = response.data;
            this.form.allotState = "ENTRY_COMPLETED";
            updateAllot(this.form).then((response) => {
              this.$modal.msgSuccess("录入完成");
              this.getList();
              this.open = false;
            });
          });
        })
        .catch(() => {});
      // getAllot(row.id).then((response) => {
      //   this.form = response.data;
      //   this.form.allotState = "ENTRY_COMPLETED";
      //   updateAllot(this.form).then((response) => {
      //     this.$modal.msgSuccess("录入完成");
      //     this.getList();
      //     this.open = false;
      //   });
      // });
    },
    /* 查看详情 */
    handleDatail(row) {
      console.log("看看", row);

      this.reset();
      this.activeName = "first";
      this.allot_id = row.id;
      const id = row.id || this.ids;
      this.openDetail = true;
      getAllot(id).then((response) => {
        this.form = response.data;
        this.form.wmsAllotDetailList = response.data.wmsAllotDetailList;
        console.log(
          "this.form.wmsAllotDetailList",
          this.form.wmsAllotDetailList
        );
        console.log("this.openDetail", this.openDetail);
        this.detailTitle = "调拨管理详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // this.form.outWarehouseId = this.warehouseIn.id;
      // console.log("this.form-------->", this.form);
      // this.outWarehouseCode =this.warehouseOut.code
      // this.outWarehouseName =this.warehouseOut.name
      // this.form.outWareType= this.warehouseOut.type
      // this.inWarehouseId =this.warehouseOut.id
      // this.inWareType  =this.warehouseIn.type
      // this.inWarehouseCode =this.warehouseIn.code
      // this.inWarehouseName =this.warehouseIn.name
      // 新增校验：检查明细表是否有数据
      if (!this.wmsAllotDetailList || this.wmsAllotDetailList.length === 0) {
        this.$message.error("请选择添加一条物料明细");
        return;
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.wmsAllotDetailList = this.wmsAllotDetailList;
          if (this.form.id != null) {
            updateAllot(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAllot(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const allotNos = row.allotNo || this.allotNos;
      this.$modal
        .confirm('是否确认删除调拨管理编号为"' + allotNos + '"的数据项？')
        .then(function () {
          return delAllot(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 调拨单明细序号 */
    rowWmsAllotDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    loadListMaterials() {
      listMaterial(this.queryParams).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        console.log("this.materialList", this.materialList);
      });
    },
    importFuction(type) {
      console.log("点击了导入按钮");

      this.warehouseDialogVisible = true;
      // this.getMaterialList();
      // 存储调入或调出，用于选中存储判断
      this.type = type;
    },
    /** 加载仓库 */
    loadListWarehouse() {
      listWarehouse(this.queryParams).then((response) => {
        this.warehouseList = response.rows;
        console.log("this.warehouseList", this.warehouseList);
      });
    },
    handleAddMaterial() {
      console.log("确定的时候this.detailmaterialList", this.detailmaterialList);

      this.detailmaterialList.forEach((obj) => {
        obj.materialId = obj.id;
        console.log("obj", obj);
        const exists = this.wmsAllotDetailList.some(
          (item) => item.materialId === obj.materialId
        );
        if (exists) {
          this.$message.warning(`物料: ${obj.materialName}已存在！`);
          return; // 跳过当前循环，避免推送到 wmsAllotDetailList
        }
        this.wmsAllotDetailList.push(obj);
      });
      this.dialogVisible = false;
      // this.$refs.materialTable.clearSelection();
      this.$refs?.materialTable?.clearSelection();
    },

    /** 调拨单明细添加按钮操作 */
    handleAddWmsAllotDetail() {
      this.dialogVisible = true;
      this.title = "新增调拨单物料列表";

      // let obj = {};
      // obj.allotNo = "";
      // obj.materialId = "";
      // obj.materialCode = "";
      // obj.materialName = "";
      // obj.specification = "";
      // obj.materialUnit = "";
      // obj.qty = "";
      // obj.incomingQty = "";
      // obj.batchNo = "";
      // obj.allotState = "";
      // obj.remark = "";
      // obj.comId = "";
      // this.wmsAllotDetailList.push(obj);
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleWarehouseClose() {
      this.warehouseDialogVisible = false;
      this.$refs?.warehouseTable?.clearSelection();

      // this.$refs?.rightMultipleTable?.clearSelection();
    },
    /** 调拨单明细删除按钮操作 */
    handleDeleteWmsAllotDetail() {
      if (this.checkedWmsAllotDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的调拨单明细数据");
      } else {
        const wmsAllotDetailList = this.wmsAllotDetailList;
        const checkedWmsAllotDetail = this.checkedWmsAllotDetail;
        this.wmsAllotDetailList = wmsAllotDetailList.filter(function (item) {
          return checkedWmsAllotDetail.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsAllotDetailSelectionChange(selection) {
      this.checkedWmsAllotDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/allot/export",
        {
          ...this.queryParams,
        },
        `调拨管理_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    receiveDetailData(data) {
      console.log("看看data", data);
      this.allot_detail_id = data;
    },
    /**标签点击 */
    handleClick() {
      this.allot_detail_id = null;
    },
  },
};
</script>
