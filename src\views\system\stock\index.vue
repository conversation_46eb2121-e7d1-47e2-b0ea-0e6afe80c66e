<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="盘点单号" prop="checkNo">
          <el-input v-model="queryParams.checkNo" placeholder="请输入盘点单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="仓库id" prop="warehouseId">
        <el-input
          v-model="queryParams.warehouseId"
          placeholder="请输入仓库id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="仓库编码" prop="warehouseCode">
        <el-input
          v-model="queryParams.warehouseCode"
          placeholder="请输入仓库编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="仓库名称" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入仓库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="库区id" prop="areaId">
        <el-input
          v-model="queryParams.areaId"
          placeholder="请输入库区id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="库区编码" prop="areaCode">
        <el-input
          v-model="queryParams.areaCode"
          placeholder="请输入库区编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="库区名称" prop="areaName">
        <el-input
          v-model="queryParams.areaName"
          placeholder="请输入库区名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="库位id" prop="locationId">
        <el-input
          v-model="queryParams.locationId"
          placeholder="请输入库位id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="库位编码" prop="locationCode">
        <el-input
          v-model="queryParams.locationCode"
          placeholder="请输入库位编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="库位名称" prop="locationName">
        <el-input
          v-model="queryParams.locationName"
          placeholder="请输入库位名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="物料id" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item label="盘点状态" prop="inventoryStatusArr">
          <el-select v-model="queryParams.inventoryStatusArr" placeholder="请选择盘点状态" clearable multiple>
            <el-option v-for="dict in dict.type.inventory_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:stock:add']">新增</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:stock:edit']"
        >修改</el-button>
      </el-col> -->
        <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:stock:remove']"
        >删除</el-button>
      </el-col> -->
        <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:stock:export']"
        >导出</el-button>
      </el-col> -->
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="stockList" @selection-change="handleSelectionChange">
        <el-table-column type="index" width="50" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="" align="center" prop="id" /> -->
        <el-table-column label="盘点单号" align="center" prop="checkNo"
          :width="tableWidth(stockList.map((x) => x.checkNo))">
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip placement="top" effect="dark" :content="scope.row.checkNo">
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.checkNo
                  }}</span>
              </el-tooltip>
              <i style=" cursor: pointer" class="el-icon-document-copy" v-clipboard:copy="scope.row.checkNo"
                v-clipboard:success="onCopy"></i>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" />
        <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
        <el-table-column label="库区编码" align="center" prop="areaCode" />
        <el-table-column label="库区名称" align="center" prop="areaName" />
        <!-- <el-table-column label="库位id" align="center" prop="locationId" /> -->
        <el-table-column label="库位编码" align="center" prop="locationCode" />
        <el-table-column label="库位名称" align="center" prop="locationName" />
        <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <!-- <el-table-column label="物料名称" align="center" prop="materialName" /> -->
        <el-table-column label="盘点状态" align="center" prop="inventoryStatus">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_status" :value="scope.row.inventoryStatus" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:stock:edit']">修改</el-button>
            <!-- 录入完成按钮：点击后将状态改为 "待入库" -->
            <el-button v-if="scope.row.inventoryStatus === 'CREATED'" size="mini" type="text" icon="el-icon-check"
              @click="handleComplete(scope.row)" v-hasPermi="['system:stock:edit']">录入完成</el-button>
            <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:stock:remove']"
          >删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- <el-table height="62vh" v-loading="loading" :data="stockDetailList" >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" />
      <el-table-column label="盘点主表id" align="center" prop="checkId" /> 
      <el-table-column label="盘点单号" align="center" prop="checkNo" />
      <el-table-column label="仓库id" align="center" prop="warehouseId" />
      <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="物料id" align="center" prop="materialId" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="盘点状态" align="center" prop="inventoryStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.inventory_status" :value="scope.row.inventoryStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="预盘量" align="center" prop="expectedQty" />
      <el-table-column label="实盘量" align="center" prop="actualQty" />
      <el-table-column label="盘赢" align="center" prop="differentQty" />
      <el-table-column label="库区id" align="center" prop="areaId" />
      <el-table-column label="库区编码" align="center" prop="areaCode" />
      <el-table-column label="库区名称" align="center" prop="areaName" />
      <el-table-column label="库位id" align="center" prop="locationId" />
      <el-table-column label="库位编码" align="center" prop="locationCode" />
      <el-table-column label="库位名称" align="center" prop="locationName" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParamsDetail.pageNum"
      :limit.sync="queryParamsDetail.pageSize"
      @pagination="getListDetail"
    /> -->
      <!-- 仓库选择对话框 -->
      <el-dialog title="选择仓库" :visible.sync="warehouseDialogVisible" width="50%">
        <el-table :data="warehouseList" v-loading="warehouseLoading" @row-click="selectWarehouse" height="400" border>
          <el-table-column prop="warehouseCode" label="仓库编码" align="center" />
          <el-table-column prop="warehouseName" label="仓库名称" align="center" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="warehouseDialogVisible = false">取消</el-button>
        </div>
      </el-dialog>

      <!-- 库位选择对话框 -->
      <!-- <el-dialog
        title="选择库位"
        :visible.sync="locationDialogVisible"
        width="50%"
      >
        <el-table
          :data="locationList"
          v-loading="locationLoading"
          @row-click="selectlocation"
          height="400"
          border
        >
          <el-table-column
            prop="locationCode"
            label="库位编码"
            align="center"
          />
          <el-table-column
            prop="locationName"
            label="库位名称"
            align="center"
          />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="locationDialogVisible = false">取消</el-button>
        </div>
      </el-dialog> -->

      <!-- 物料选择对话框 -->
      <el-dialog title="选择物料" :visible.sync="materialDialogVisible" width="50%">
        <el-table :data="materialList" v-loading="materialLoading" @row-click="selectMaterial" height="400" border>
          <el-table-column prop="materialCode" label="物料编码" align="center" />
          <el-table-column prop="materialName" label="物料名称" align="center" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="materialDialogVisible = false">取消</el-button>
        </div>
      </el-dialog>

      <!-- 库区选择对话框 -->
      <el-dialog title="选择库区" :visible.sync="areaDialogVisible" width="50%">
        <el-table :data="areaList" v-loading="areaLoading" @row-click="selectarea" height="400" border>
          <el-table-column prop="areaCode" label="库区编码" align="center" />
          <el-table-column prop="areaName" label="库区名称" align="center" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="areaDialogVisible = false">取消</el-button>
        </div>
      </el-dialog>

      <!-- 明细添加物料对话框 -->
      <!-- <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >
        <el-form :data="materialList" :inline="true" label-width="100px">
          <el-table
            ref="materialTable"
            :data="materialList"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChangematerial"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column
              label="物料编码"
              align="center"
              prop="materialCode"
            />
            <el-table-column
              label="物料名称"
              align="center"
              prop="materialName"
            />
            <el-table-column
              label="规格型号"
              align="center"
              prop="specification"
            />
            <el-table-column label="单位" align="center" prop="materialUnit" />
          </el-table>
        </el-form>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadListMaterials"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleAddMaterial">确 定</el-button>
        </span>
      </el-dialog> -->

      <!-- 添加或修改盘点主表对话框 -->
      <el-dialog :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="盘点单信息" name="1">
              <!-- <el-form-item label="" prop="checkNo" style="width: 240px;">
          <el-input v-model="form.checkNo" placeholder="请输入" />
        </el-form-item> -->
              <!-- <el-form-item label="仓库id" prop="warehouseId" style="width: 240px;">
          <el-input v-model="form.warehouseId" placeholder="请输入仓库id" />
        </el-form-item> -->
             
                <!-- <el-col :span="10">
              <el-form-item
                label="仓库编码"
                prop="warehouseCode"
                style="width: 240px"
              >
                <el-input
                  v-model="form.warehouseCode"
                  placeholder="请输入仓库编码"
                  @focus="openWarehouseDialog"
                />
              </el-form-item>
            </el-col> -->
            <el-row class="mb8">
                <el-col :span="12">
                  <el-form-item label="仓库名称" prop="warehouseName" style="width: 240px;">
                    <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" @focus="openWarehouseDialog" />
                  </el-form-item>
                </el-col>
              
              <!-- <el-form-item label="库区id" prop="areaId" style="width: 240px;">
          <el-input v-model="form.areaId" placeholder="请输入库区id" />
        </el-form-item> -->
              
                <el-col :span="10">
                  <el-form-item label="盘点类型" prop="inventoryType" style="width: 240px;">
                    <el-select v-model="queryParams.inventoryType" placeholder="请选择盘点类型" clearable
                      @change="handleInventoryTypeChange">
                      <el-option v-for="option in inventoryTypeOptions" :key="option.value" :label="option.label"
                        :value="option.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
            </el-row>
              <el-row :gutter="10" class="mb8">
                <!-- <el-col :span="10">
              <el-form-item
                v-if="queryParams.inventoryType === 'area'"
                label="库区编码"
                prop="areaCode"
                style="width: 240px"
              >
                <el-input
                  v-model="form.areaCode"
                  placeholder="请输入库区编码"
                  @focus="openAreaDialog"
                />
              </el-form-item>
            </el-col> -->
                <el-col :span="10">
                  <el-form-item v-if="queryParams.inventoryType === 'area'" label="库区名称" prop="areaName"
                    style="width: 240px">
                    <el-input v-model="form.areaName" placeholder="请输入库区名称" @focus="openAreaDialog" />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-form-item label="库位id" prop="locationId" style="width: 240px;">
          <el-input v-model="form.locationId" placeholder="请输入库位id" />
        </el-form-item> -->

              <!-- <el-row :gutter="10" class="mb8">
        <el-col :span="10">
        <el-form-item label="库位编码" prop="locationCode" style="width: 240px;">
          <el-input v-model="form.locationCode" 
          placeholder="请输入库位编码" 
          @focus="openLocationDialog"
          />
        </el-form-item>
        </el-col>
        <el-col :span="10">
        <el-form-item label="库位名称" prop="locationName" style="width: 240px;">
          <el-input v-model="form.locationName" placeholder="请输入库位名称" disabled/>
        </el-form-item>
        </el-col>
        </el-row> -->

              <!-- <el-form-item label="物料id" prop="materialId" style="width: 240px;">
          <el-input v-model="form.materialId" placeholder="请输入物料id" />
        </el-form-item> -->
              <el-row :gutter="10" class="mb8">
                <!-- <el-col :span="10">
              <el-form-item
                v-if="queryParams.inventoryType === 'material'"
                label="物料编码"
                prop="materialCode"
                style="width: 240px"
              >
                <el-input
                  v-model="form.materialCode"
                  placeholder="请输入物料编码"
                  @focus="openMaterialDialog"
                />
              </el-form-item>
            </el-col> -->
                <el-col :span="10">
                  <el-form-item v-if="queryParams.inventoryType === 'material'" label="物料名称" prop="materialName"
                    style="width: 240px">
                    <el-input v-model="form.materialName" placeholder="请输入物料名称" @focus="openMaterialDialog" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
            <el-collapse-item title="盘点单明细信息" name="2">
              <el-table height="62vh" v-loading="loading" :data="inventoryList"
                @selection-change="handleSelectionChangeInventory">
                <!-- <el-table-column type="selection" width="55" align="center" /> -->

                <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
                <el-table-column label="仓库名称" align="center" prop="warehouseName" />
                <el-table-column label="物料编码" align="center" prop="materialCode" />
                <el-table-column label="物料名称" align="center" prop="materialName" />
                <el-table-column label="数量" align="center" width="100" prop="qty" />

                <!-- <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleAddInventory(scope.row)"
                  v-hasPermi="['system:stock:add']"
                  >添加</el-button
                >
              </template>
            </el-table-column> -->
              </el-table>
              <!-- <el-form-item label="盘点状态" prop="inventoryStatus" style="width: 240px;">
          <el-radio-group v-model="form.inventoryStatus">
            <el-radio
              v-for="dict in dict.type.inventory_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item> -->
              <!-- <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->

              <!-- <el-divider content-position="center">盘点明细信息</el-divider> -->
              <!-- <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAddWmsTakeStockDetail"
                >添加</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                @click="handleDeleteWmsTakeStockDetail"
                >删除</el-button
              >
            </el-col>
          </el-row> -->
              <!-- <el-table
            :data="wmsTakeStockDetailList"
            :row-class-name="rowWmsTakeStockDetailIndex"
            @selection-change="handleWmsTakeStockDetailSelectionChange"
            ref="wmsTakeStockDetail"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              label="序号"
              align="center"
              prop="index"
              width="50"
            />
            <el-table-column label="盘点单号" prop="checkNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.checkNo" placeholder="请输入盘点单号" />
            </template>
          </el-table-column>
            <el-table-column label="仓库id" prop="warehouseId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.warehouseId" placeholder="请输入仓库id" />
            </template>
          </el-table-column>
            <el-table-column label="仓库编码" prop="warehouseCode" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.warehouseCode" placeholder="请输入仓库编码" />
            </template>
          </el-table-column>
            <el-table-column label="仓库名称" prop="warehouseName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.warehouseName" placeholder="请输入仓库名称" />
            </template>
          </el-table-column>
            <el-table-column label="物料id" prop="materialId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.materialId" placeholder="请输入物料id" />
            </template>
          </el-table-column>
            <el-table-column label="物料编码" prop="materialCode" width="150">
              <template slot-scope="scope">
              <el-input v-model="scope.row.materialCode" placeholder="请输入物料编码" />
            </template>
            </el-table-column>
            <el-table-column label="物料名称" prop="materialName" width="150">
              <template slot-scope="scope">
              <el-input v-model="scope.row.materialName" placeholder="请输入物料名称" />
            </template>
            </el-table-column>
            <el-table-column label="盘点状态" prop="inventoryStatus" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.inventoryStatus" placeholder="请选择盘点状态">
                <el-option
                  v-for="dict in dict.type.inventory_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
            <el-table-column label="预盘量" prop="expectedQty" width="150">
              <template slot-scope="scope">
              <el-input v-model="scope.row.expectedQty" placeholder="请输入预盘量" />
            </template>
            </el-table-column>
            <el-table-column label="实盘量" prop="actualQty" width="150">
              <template slot-scope="scope">
              <el-input v-model="scope.row.actualQty" placeholder="请输入实盘量" />
            </template>
            </el-table-column>
            <el-table-column label="盘赢" prop="differentQty" width="150">
              <template slot-scope="scope">
              <el-input v-model="scope.row.differentQty" placeholder="请输入盘赢" />
            </template>
            </el-table-column>
            <el-table-column label="库区id" prop="areaId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.areaId" placeholder="请输入库区id" />
            </template>
          </el-table-column>
            <el-table-column label="库区编码" prop="areaCode" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.areaCode" placeholder="请输入库区编码" />
            </template>
          </el-table-column>
            <el-table-column label="库区名称" prop="areaName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.areaName" placeholder="请输入库区名称" />
            </template>
          </el-table-column>
            <el-table-column label="库位id" prop="locationId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.locationId" placeholder="请输入库位id" />
            </template>
          </el-table-column>
            <el-table-column label="库位编码" prop="locationCode" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.locationCode" placeholder="请输入库位编码" />
            </template>
          </el-table-column>
            <el-table-column label="库位名称" prop="locationName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.locationName" placeholder="请输入库位名称" />
            </template>
          </el-table-column>
            <el-table-column label="组织" prop="comId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.comId" placeholder="请输入组织" />
            </template>
          </el-table-column>
          </el-table> -->
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div style="margin: 5px; display: flex; justify-content: end">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listStock,
  getStock,
  delStock,
  addStock,
  updateStock,
} from "@/api/system/stock";
import { listMaterial } from "@/api/system/material";
import { listWarehouse } from "@/api/system/warehouse";
import { listStockDetail } from "@/api/system/stockDetail";
import {
  listArea,
  getArea,
  delArea,
  addArea,
  updateArea,
} from "@/api/system/area";
import { listWarehouse_location } from "@/api/system/warehouse_location";
import { listInventorygp } from "@/api/system/inventory";
export default {
  name: "Stock",
  dicts: ["inventory_status"],
  data() {
    return {
      warehouseDialogVisible: false, // 仓库选择弹窗是否显示
      warehouseList: [], // 仓库列表数据
      warehouseLoading: false, // 仓库列表加载状态

      locationDialogVisible: false, // 库位选择弹窗是否显示
      locationList: [], // 库位列表数据
      locationLoading: false, // 库位列表加载状态

      areaDialogVisible: false, // 库区选择弹窗是否显示
      areaList: [], // 库区列表数据
      areaLoading: false, // 库区列表加载状态

      materialDialogVisible: false, // 物料选择弹窗是否显示
      materialList: [], // 物料列表数据
      materialLoading: false, // 物料列表加载状态

      inventoryLoading: false, // 物料列表加载状态
      inventoryList: [], // 物料列表数据

      // 遮罩层
      loading: true,
      activeNames: ["1", "2"],
      activeNamesInfo: ["1", "2"],
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsTakeStockDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 盘点主表表格数据
      stockList: [],
      // 盘点明细表格数据
      wmsTakeStockDetailList: [],
      // 选中的物料数据
      detailmaterialList: [],
      // 控制明细添加物料对话框
      dialogVisible: false,
      //获取物料列表
      materialList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        inventoryStatus: null,
        inventoryStatusArr: [],
        comId: null,
        inventoryType: null,
      },
      inventoryTypeOptions: [
        { label: "全盘", value: "full" },
        { label: "库区", value: "area" },
        { label: "物料", value: "material" },
      ],
      // 明细表查询参数
      queryParamsDetail: {
        pageNum: 1,
        pageSize: 10,
        checkId: null,
        checkNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        inventoryStatus: null,
        expectedQty: null,
        actualQty: null,
        differentQty: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        comId: null,
      },
      // 表单参数
      form: {
        wmsTakeStockDetailList: [],
      },
      // 表单校验
      rules: {
        checkNo: [{ required: true, message: "不能为空", trigger: "blur" }],
        // warehouseCode: [
        //   { required: true, message: "仓库编码不能为空", trigger: "blur" },
        // ],
        // areaCode: [
        //   { required: true, message: "库区编码不能为空", trigger: "blur" },
        // ],
        // locationCode: [
        //   { required: true, message: "库位编码不能为空", trigger: "blur" },
        // ],
        // materialCode: [
        //   { required: true, message: "物料编码不能为空", trigger: "blur" },
        // ],
        warehouseName: [
          { required: true, message: "仓库名称不能为空", trigger: "blur" },
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.loadListMaterials();
    this.getListDetail();
  },
  methods: {
    /** 查询盘点主表列表 */
    getList() {
      this.loading = true;
      listStock(this.queryParams).then((response) => {
        // this.stockList = this.sortArrayByField(response.rows, 'createTime');
        this.stockList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    resetQueryPram() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        checkNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        inventoryStatus: null, // 保留默认值
        comId: null,
      };
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        checkNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        inventoryStatus: null, // 保留默认值,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.wmsTakeStockDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // wms库存单多选框选中数据
    handleSelectionChangeInventory(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      const createdState = this.dict.type.inventory_status.find(
        (item) => item.label === "已创建"
      );
      this.form.inventoryStatus = createdState ? createdState.value : null; // 设置默认状态
      console.log("默认盘点状态：", this.form.inventoryStatus); // 打印默认值
      this.open = true;
      this.title = "添加盘点主表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStock(id).then((response) => {
        this.form = response.data;
        this.wmsTakeStockDetailList = response.data.wmsTakeStockDetailList;
        this.open = true;
        this.title = "修改盘点主表";
      });
    },
    // 录入完成按钮操作
    handleComplete(row) {
      // 确认操作
      const ids = row.checkNo || this.ids;
      this.$modal
        .confirm('是否确认将盘点单号为"' + ids + '"状态改为 "待盘点"？')
        .then(() => {
          // 从 dict 中获取“待盘点”的值
          const createdState = this.dict.type.inventory_status.find(
            (item) => item.label === "待盘点"
          );
          row.inventoryStatus = createdState ? createdState.value : null; // 设置默认状态
          // 更新明细表状态
          console.log("更新后的状态：", row.inventoryStatus);
          // 调用后端接口更新状态
          updateStock(row).then(() => {
            this.$modal.msgSuccess("状态已更新为待盘点");
            this.getList(); // 刷新列表
          });
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.resetQueryPram();
      // 校验主表数据是否存在
      if (!this.form.warehouseCode) {
        this.$modal.msgError("请新增至少一条移库信息");
        return; // 阻止提交
      }
      // 校验明细表数据是否存在
      // if (this.wmsTakeStockDetailList.length === 0) {
      //   this.$modal.msgError("请至少新增一条明细数据");
      //   return; // 阻止提交
      // }
      //更新主表
      const firstItem = this.inventoryList[0];
      this.form = {
        ...this.form,
        materialId: firstItem.materialId,
        materialCode: firstItem.materialCode,
        materialName: firstItem.materialName,
        inventoryType: this.form.inventoryType,
      };
      // 将所有库存数据添加到明细表
      this.inventoryList.forEach(item => {
        const newDetail = {
          checkNo: this.form.checkNo,
          warehouseId: item.warehouseId,
          warehouseCode: item.warehouseCode,
          warehouseName: item.warehouseName,
          materialId: item.materialId,
          materialCode: item.materialCode,
          materialName: item.materialName,
          inventoryStatus: this.form.inventoryStatus,
          expectedQty: item.qty,
          actualQty: null,
          differentQty: null,
          areaId: this.form.areaId,
          areaCode: this.form.areaCode,
          areaName: this.form.areaName,
          locationId: this.form.locationId,
          locationCode: this.form.locationCode,
          locationName: this.form.locationName,
          comId: null
        };
        this.wmsTakeStockDetailList.push(newDetail);
      });
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.wmsTakeStockDetailList = this.wmsTakeStockDetailList;
          console.log("提交的数据：", this.form); // 打印提交的数据
          if (this.form.id != null) {
            updateStock(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reset();
              this.getList();
            });
          } else {
            addStock(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reset();
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除该数据项？")
        .then(function () {
          return delStock(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /**将WMS库存单的数据作为新增数据放到盘点表中 */
    handleAddInventory(row) {
      // 检查是否已存在相同的物料编码
      const exists = this.wmsTakeStockDetailList.some(
        (item) => item.materialCode === row.materialCode
      );
      // 1. 更新主表数据
      this.form = {
        ...this.form,
        warehouseId: row.warehouseId,
        warehouseCode: row.warehouseCode,
        warehouseName: row.warehouseName,
        materialId: row.materialId,
        materialCode: row.materialCode,
        materialName: row.materialName,
        areaId: this.form.areaId,
        areaCode: this.form.areaCode,
        areaName: this.form.areaName,
        // 如果需要保持其他字段不变，使用扩展运算符保留
      };
      // if (exists) {
      //   this.$modal.msgError("该物料编码已存在，请选择其他物料");
      //   return;
      // }
      // 将选中的数据添加到明细表中
      const newDetail = {
        checkNo: this.form.checkNo,
        warehouseId: row.warehouseId,
        warehouseCode: row.warehouseCode,
        warehouseName: row.warehouseName,
        materialId: row.materialId,
        materialCode: row.materialCode,
        materialName: row.materialName,
        inventoryStatus: this.form.inventoryStatus,
        expectedQty: null,
        actualQty: null,
        differentQty: null,
        areaId: this.form.areaId,
        areaCode: this.form.areaCode,
        areaName: this.form.areaName,
        locationId: this.form.locationId,
        locationCode: this.form.locationCode,
        locationName: this.form.locationName,
        comId: null,
      };
      this.wmsTakeStockDetailList.push(newDetail);
    },
    /** 盘点明细序号 */
    rowWmsTakeStockDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 盘点明细添加按钮操作 */
    handleAddWmsTakeStockDetail() {
      this.dialogVisible = true;

      // 清空选中的物料数据
      this.detailmaterialList = [];
      this.ids = [];

      // 清除表格选中状态
      this.$nextTick(() => {
        const tableRef = this.$refs.materialTable; // 获取表格的 ref
        if (tableRef) {
          tableRef.clearSelection(); // 清除选中状态
        }
      });
      // let obj = {};
      // obj.checkNo = "";
      // obj.warehouseId = "";
      // obj.warehouseCode = "";
      // obj.warehouseName = "";
      // obj.materialId = "";
      // obj.materialCode = "";
      // obj.materialName = "";
      // obj.inventoryStatus = "";
      // obj.expectedQty = "";
      // obj.actualQty = "";
      // obj.differentQty = "";
      // obj.areaId = "";
      // obj.areaCode = "";
      // obj.areaName = "";
      // obj.locationId = "";
      // obj.locationCode = "";
      // obj.locationName = "";
      // obj.remark = "";
      // obj.comId = "";
      // this.wmsTakeStockDetailList.push(obj);
    },
    /** 盘点明细删除按钮操作 */
    handleDeleteWmsTakeStockDetail() {
      if (this.checkedWmsTakeStockDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的盘点明细数据");
      } else {
        const wmsTakeStockDetailList = this.wmsTakeStockDetailList;
        const checkedWmsTakeStockDetail = this.checkedWmsTakeStockDetail;
        this.wmsTakeStockDetailList = wmsTakeStockDetailList.filter(function (
          item
        ) {
          return checkedWmsTakeStockDetail.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsTakeStockDetailSelectionChange(selection) {
      this.checkedWmsTakeStockDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stock/export",
        {
          ...this.queryParams,
        },
        `盘点主表_${new Date().toLocaleDateString()}.xlsx`
      );
    },

    // 主表行点击事件
    handleRowClick(row) {
      // 更新查询参数，设置主表 id
      this.queryParamsDetail.moveId = row.id;
      // 调用获取明细数据的方法
      this.getListDetail();
    },
    // 选择仓库
    selectWarehouse(row) {
      this.form.warehouseCode = row.warehouseCode; // 将仓库编码赋值到移库表单
      this.form.warehouseName = row.warehouseName; // 将仓库名称赋值到移库表单
      this.form.warehouseId = row.id; // 将仓库ID赋值到移库表单
      this.warehouseDialogVisible = false; // 关闭弹窗
    },
    // 选择库区
    selectarea(row) {
      this.form.areaCode = row.areaCode; // 将库区编码赋值到移库表单
      this.form.areaName = row.areaName; // 将库区名称赋值到移库表单
      this.form.areaId = row.id; // 将库区ID赋值到移库表单
      this.queryParams.areaCode = row.areaCode; // 将库区编码赋值到查询参数
      this.queryParams.areaName = row.areaName; // 将库区名称赋值到查询参数
      this.queryParams.areaId = row.id; // 将库区ID赋值到查询参数
      this.queryParams.warehouseId = this.form.warehouseId; // 将仓库ID赋值到查询参数
      this.queryParams.warehouseCode = this.form.warehouseCode; // 将仓库编码赋值到查询参数
      this.areaDialogVisible = false; // 关闭弹窗

      // 调用加载盘点类型列表的方法,查询对应的数据返回前端并展示
      this.loadInventoryList();
    },
    // 选择库位
    selectlocation(row) {
      this.form.locationCode = row.locationCode; // 将库位编码赋值到移库表单
      this.form.locationName = row.locationName; // 将库位名称赋值到移库表单
      this.form.locationId = row.id; // 将库位ID赋值到移库表单
      this.locationDialogVisible = false; // 关闭弹窗
    },
    // 选择物料
    selectMaterial(row) {
      this.form.materialCode = row.materialCode; // 将物料编码赋值到移库表单
      this.form.materialName = row.materialName; // 将物料名称赋值到移库表单
      this.form.materialId = row.id; // 将物料ID赋值到移库表单
      this.queryParams.materialCode = row.materialCode; // 将物料编码赋值到查询参数
      this.queryParams.materialName = row.materialName; // 将物料名称赋值到查询参数
      this.queryParams.materialId = row.id; // 将物料ID赋值到查询参数
      this.queryParams.warehouseId = this.form.warehouseId; // 将仓库ID赋值到查询参数
      this.queryParams.warehouseCode = this.form.warehouseCode; // 将仓库编码赋值到查询参数
      this.materialDialogVisible = false; // 关闭弹窗

      // 调用加载盘点类型列表的方法,查询对应的数据返回前端并展示
      this.loadInventoryList();
    },
    // 打开仓库选择弹窗
    openWarehouseDialog() {
      this.warehouseDialogVisible = true;
      this.loadWarehouseList();
    },
    // 打开库区选择弹窗
    openAreaDialog() {
      this.areaDialogVisible = true;
      this.loadAreaList();
    },
    // 打开库位选择弹窗
    openLocationDialog() {
      this.locationDialogVisible = true;
      this.loadLocationList();
    },
    // 打开物料选择弹窗
    openMaterialDialog() {
      this.materialDialogVisible = true;
      this.loadListMaterial();
    },

    // 加载仓库列表数据
    loadWarehouseList() {
      this.warehouseLoading = true;
      listWarehouse()
        .then((response) => {
          this.warehouseList = response.rows; // 假设接口返回的数据在 rows 中
          this.warehouseLoading = false;
        })
        .catch(() => {
          this.warehouseLoading = false;
        });
    },
    // 加载库区列表数据
    loadAreaList() {
      this.areaLoading = true;
      listArea()
        .then((response) => {
          this.areaList = response.rows; // 假设接口返回的数据在 rows 中
          this.areaLoading = false;
        })
        .catch(() => {
          this.areaLoading = false;
        });
    },
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
    //加载wms库存表
    loadInventoryList() {
      this.inventoryLoading = true;
      listInventorygp(this.queryParams)
        .then((response) => {
          this.inventoryList = response.rows; // 假设接口返回的数据在 rows 中
          this.inventoryLoading = false;
        })
        .catch(() => {
          this.inventoryLoading = false;
        });
    },
    // 处理盘点类型变化
    handleInventoryTypeChange(value) {
      if (value === "full") {
        // 当选择全盘时
        this.queryParams.warehouseId = this.form.warehouseId; // 将仓库ID赋值到查询参数
        this.queryParams.warehouseCode = this.form.warehouseCode; // 将仓库编码赋值到查询参数

        // 直接调用加载库存列表
        this.loadInventoryList();
      } else {
        // 清空库存列表
        this.inventoryList = [];
      }
    },

    // 加载物料列表数据
    loadListMaterial() {
      this.materialLoading = true;
      listMaterial()
        .then((response) => {
          this.materialList = response.rows; // 假设接口返回的数据在 rows 中
          this.materialLoading = false;
        })
        .catch(() => {
          this.materialLoading = false;
        });
    },
    // 加载库位列表数据
    loadLocationList() {
      this.locationLoading = true;
      listWarehouse_location()
        .then((response) => {
          this.locationList = response.rows; // 假设接口返回的数据在 rows 中
          this.locationLoading = false;
        })
        .catch(() => {
          this.locationLoading = false;
        });
    },
    loadListMaterials() {
      listMaterial(this.queryParams).then((response) => {
        this.materialList = response.rows;
        //this.total = response.total;
        console.log("this.materialList", this.materialList);
      });
    },

    handleSelectionChangematerial(selection) {
      console.log("当前选中的数据：", selection); // 打印选中的数据
      this.ids = selection.map((item) => item.id);
      console.log("选中的ID列表：", this.ids);
      // 获取选中项的ID数组

      // 根据ID筛选需要提交的数据
      this.detailmaterialList = this.materialList.filter((item) =>
        this.ids.includes(item.id)
      );
      console.log("筛选后的提交数据：", this.detailmaterialList);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleAddMaterial() {
      console.log("确定的时候this.detailmaterialList", this.detailmaterialList);
      if (this.detailmaterialList.length === 0) {
        this.$modal.msgError("请至少选择一条物料数据");
        return; // 阻止提交
      }
      this.detailmaterialList.forEach((obj) => {
        obj.materialId = obj.id;
        console.log("obj", obj);
        this.wmsTakeStockDetailList.push(obj);
      });
      this.dialogVisible = false;
    },
    handleClose() {
      this.dialogVisible = false;
      // 清除表格选中状态
      this.$nextTick(() => {
        const tableRef = this.$refs.materialTable; // 获取表格的 ref
        if (tableRef) {
          tableRef.clearSelection(); // 清除选中状态
        }
      });

      // 清空选中的物料数据
      this.detailmaterialList = [];
      this.ids = [];
    },

    /** 查询盘点明细列表 */
    getListDetail() {
      this.loading = true;
      listStockDetail(this.queryParams).then((response) => {
        this.stockDetailList = response.rows;
        //this.total = response.total;
        this.loading = false;
      });
    },
  },
};
</script>
