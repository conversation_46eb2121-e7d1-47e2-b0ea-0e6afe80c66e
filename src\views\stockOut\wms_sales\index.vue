<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="出库单号" prop="stockOutNo">
          <el-input
            v-model="queryParams.stockOutNo"
            placeholder="请输入出库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="出库日期" prop="stockOutDate">
          <el-date-picker
            clearable
            v-model="queryParams.stockOutDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出库日期"
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="出库类型" prop="stockOutType">
          <el-select
            disabled
            v-model="queryParams.stockOutType"
            placeholder="请选择出库类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.stock_out_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="出库状态" prop="stockOutStateArr">
          <el-select
            multiple
            v-model="queryParams.stockOutStateArr"
            placeholder="请选择出库状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.stock_out_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="质检状态" prop="qualityState">
          <el-select
            v-model="queryParams.qualityState"
            placeholder="请选择质检状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.quality_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item> -->

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:wms_sales:add']"
            >新增</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:wms_sales:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:wms_sales:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:wms_sales:export']"
            >导出</el-button
          >
        </el-col> -->
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-table
        height="62vh"
        v-loading="loading"
        :data="wms_salesList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" width="55" align="center" />
        <el-table-column
          label="出库单号"
          align="center"
          prop="stockOutNo"
          :width="tableWidth(wms_salesList.map((x) => x.stockOutNo))"
        />
        <el-table-column
          label="出库日期"
          align="center"
          prop="stockOutDate"
          min-width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.stockOutDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="收货方"
          align="center"
          prop="stockOutNo"
          :width="tableWidth(wms_salesList.map((x) => x.stockOutNo))"
        />

        <el-table-column label="退货方式" align="center" prop="stockOutNo">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.return_method"
              :value="scope.row.returnMethod"
            />
          </template>
        </el-table-column>
        <el-table-column label="出库状态" align="center" prop="stockOutState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.stock_out_state"
              :value="scope.row.stockOutState"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="创建人" align="center" prop="createBy" /> -->
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          min-width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="修改人" align="center" prop="updateBy" /> -->
        <el-table-column
          label="修改时间"
          align="center"
          prop="updateTime"
          min-width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          min-width="250px"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleUpdate(scope.row)"
              v-show="scope.row.stockOutState == 'TO_BE_CREATED'"
              v-hasPermi="['system:wms_sales:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handlecomplete(scope.row)"
              v-show="scope.row.stockOutState == 'TO_BE_CREATED'"
              v-hasPermi="['system:wms_sales:edit']"
            >
              录入完成
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete-solid"
              @click="handlecancellation(scope.row)"
              v-show="scope.row.stockOutState == 'TO_BE_CREATED'"
              v-hasPermi="['system:wms_sales:remove']"
            >
              作废
            </el-button>
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:wms_sales:remove']"
              >删除</el-button
            > -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-order"
              @click="handleDatail(scope.row)"
              v-hasPermi="['system:wms_sales:edit']"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 添加或修改销售退货对话单 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="销售发货单信息" name="1">
              <!-- 第一行 -->
              <el-row :gutter="10" class="mb8">
                <!-- <el-col :span="6">
              <el-form-item label="出库单号" prop="stockOutNo">
                <el-input
                  v-model="form.stockOutNo"
                  placeholder="请输入出库单号"
                />
              </el-form-item>
            </el-col> -->
                <el-col :span="8">
                  <el-form-item disabled label="出库类型" prop="stockOutType">
                    <el-select
                      disabled
                      v-model="form.stockOutType"
                      placeholder="请选择出库类型"
                    >
                      <el-option
                        v-for="dict in dict.type.stock_out_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="出库日期" prop="stockOutDate">
                    <el-date-picker
                      clearable
                      v-model="form.stockOutDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择出库日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    label="收货方"
                    prop="consignee"
                    style="width: 225px"
                  >
                    <el-input
                      v-model="form.consignee"
                      placeholder="请输入收货方"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 第二行 -->
              <el-row :gutter="10" class="mb8">
                <el-col :span="8">
                  <el-form-item label="退货方式" prop="returnMethod">
                    <!-- <el-form-item label="退货方式" prop="stockOutNo">
                  <el-input
                    v-model="form.stockOutNo"
                    placeholder="请选择退货方式"
                  />
                </el-form-item> -->
                    <el-select
                      label="退货方式"
                      placeholder="请选择退货方式"
                      v-model="form.returnMethod"
                      clearable
                    >
                      <el-option
                        v-for="dict in dict.type.return_method"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="6">
              <el-form-item label="出库状态" prop="stockOutState">
                <el-select
                  disabled
                  v-model="form.stockOutState"
                  placeholder="请选择出库状态"
                >
                  <el-option
                    v-for="dict in dict.type.stock_out_state"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
                <el-col :span="8">
                  <el-form-item
                    label="采购单号"
                    prop="purchaseNo"
                    style="width: 225px"
                  >
                    <el-input
                      @focus="importFuction"
                      v-model="form.purchaseNo"
                      placeholder="请选择采购单号"
                      ref="purchaseInput"
                      :disabled="isPurchaseOrderDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    label="供应商编码"
                    prop="supplierCode"
                    style="width: 225px"
                  >
                    <el-input
                      disabled
                      v-model="form.supplierCode"
                      placeholder="请输入"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 第三行 -->
              <el-row :gutter="10" class="mb8">
                <el-col :span="8">
                  <el-form-item
                    label="供应商名称"
                    prop="supplierName"
                    style="width: 225px"
                  >
                    <el-input
                      disabled
                      v-model="form.supplierName"
                      placeholder="请输入"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      placeholder="请输入内容"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
            <el-collapse-item title="出库单明细信息" name="2">
              <el-row :gutter="10" class="mb8" style="margin-left: 5px">
                <!-- <el-col :span="1.5">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAddWmsStockOutDetail"
              >
                添加
              </el-button>
            </el-col> -->
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="handleDeleteWmsStockOutDetail"
                  >
                    删除
                  </el-button>
                </el-col>
              </el-row>

              <el-table
                :data="form.wmsStockOutDetailList"
                :row-class-name="rowWmsStockOutDetailIndex"
                @selection-change="handleWmsStockOutDetailSelectionChange"
                ref="wmsStockOutDetail"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column
                  label="序号"
                  align="center"
                  prop="index"
                  width="50"
                />
                <el-table-column
                  label="物料编码"
                  prop="materialCode"
                  width="150"
                >
                </el-table-column>
                <el-table-column
                  label="物料名称"
                  prop="materialName"
                  width="150"
                >
                </el-table-column>
                <el-table-column
                  label="规格型号"
                  prop="specification"
                  width="150"
                >
                </el-table-column>
                <el-table-column label="单位" prop="materialUnit" width="150">
                </el-table-column>
                <el-table-column label="数量" prop="qty">
                  <template slot-scope="scope">
                   <el-form-item
                      :prop="'wmsStockOutDetailList.' + scope.$index + '.qty'"
                      :rules="rules.qty"
                      class="qty-form-item"
                    >
                      <el-input
                        v-model="scope.row.qty"
                        placeholder="请输入数量"
                         @input="handleQtyInput(scope.row)"
                      ></el-input>
                    </el-form-item>
                    <!-- 错误提示 - 完整显示在输入框下方 -->
                    <div v-show="scope.row.qtyError" class="error-tip" style="margin-top: 5px;">
              {{ scope.row.qtyError }}
            </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>

      <!-- 详情对话框 -->
      <el-drawer
        :title="detailTitle"
        :visible.sync="openDetail"
        :size="'75%'"
        append-to-body
      >
        <el-form
          ref="form"
          :model="form"
          size="small"
          :inline="true"
          label-width="100px"
        >
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="销售发货单信息" name="1">
              <el-form-item
                label="出库单号"
                prop="stockOutNo"
                style="width: 340px"
              >
                <el-input disabled v-model="form.stockOutNo" placeholder="" />
              </el-form-item>
              <el-form-item
                label="采购单号"
                prop="purchaseNo"
                style="width: 340px"
              >
                <el-input disabled v-model="form.purchaseNo" placeholder="" />
              </el-form-item>
              <el-form-item
                label="供应商编码"
                prop="supplierCode"
                style="width: 340px"
              >
                <el-input disabled v-model="form.supplierCode" placeholder="" />
              </el-form-item>
              <el-form-item
                label="供应商名称"
                prop="supplierName"
                style="width: 340px"
              >
                <el-input disabled v-model="form.supplierName" placeholder="" />
              </el-form-item>
              <el-form-item
                label="收货方"
                prop="consignee"
                style="width: 340px"
              >
                <el-input disabled v-model="form.consignee" placeholder="" />
              </el-form-item>
              <el-form-item
                label="出库日期"
                prop="stockOutDate"
                style="width: 340px"
              >
                <el-input disabled v-model="form.stockOutDate" placeholder="" />
              </el-form-item>
              <el-form-item
                disabled
                label="出库类型"
                prop="stockOutType"
                style="width: 340px"
              >
                <el-select
                  disabled
                  v-model="form.stockOutType"
                  placeholder="请选择出库类型"
                  clearable
                >
                  <el-option
                    disabled
                    v-for="dict in dict.type.stock_out_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item disabled label="出库状态" prop="stockOutState">
            <el-select
              disabled
              v-model="form.stockOutState"
              placeholder="请选择出库状态"
              clearable
            >
              <el-option
                v-for="dict in dict.type.stock_out_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item> -->
              <!-- <el-form-item disabled label="质检状态" prop="qualityState">
            <el-select
              disabled
              v-model="form.qualityState"
              placeholder="请选择质检状态"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in dict.type.quality_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item> -->
            </el-collapse-item>
            <el-collapse-item title="出库单明细信息" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane
                  label="入库明细"
                  name="first"
                  :data="form.wmsStockOutDetailList"
                >
                  <Outdetail
                    :stock_out_id="stock_out_id"
                    :key="stock_out_id"
                    :activeName="activeName"
                  />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <Outdetail
                        @sendDetailId="receiveDetailData"
                        :stock_out_id="stock_out_id"
                        :key="stock_out_id"
                        :activeName="activeName"
                        :tableData="form.wmsStockOutDetailList"
                      />
                    </div>
                    <div style="width: 48%">
                      <OutBox
                        :stock_detail_id="stock_detail_id"
                        :key="stock_detail_id"
                      />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>

      <!-- 采购单选择抽屉 -->
      <el-drawer
        :title="purchaseTitle"
        :visible.sync="purchaseDrawerVisible"
        :size="'60%'"
        append-to-body
        @close="handleDrawerClose"
      >
        <el-row :gutter="10">
          <!-- 左侧表单 -->
          <el-col :span="12">
            <el-form
              ref="leftForm"
              :model="purchaseQueryParams"
              size="small"
              label-width="100px"
              class="left-form"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="7">
                  <el-form-item label="采购单号">
                    <el-input v-model="purchaseQueryParams.purchaseNo" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="供应商编码">
                    <el-input v-model="purchaseQueryParams.supplierCode" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="供应商名称">
                    <el-input v-model="purchaseQueryParams.supplierName" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handlePurchaseQuery"
                      >搜索</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetPurchaseQuery"
                      >重置</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <el-table
                :data="purchaseList"
                highlight-current-row
                style="cursor: pointer"
                v-loading="purchaseLoading"
                height="40vh"
                @selection-change="handleSelectionChangePurchase"
                @select-all="onSelectAll"
                @row-click="rowClick"
                ref="leftMultipleTable"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="采购单号" prop="purchaseNo" />
                <el-table-column label="供应商编码" prop="supplierCode" />
                <el-table-column label="供应商名称" prop="supplierName" />
              </el-table>
              <pagination
                v-show="purchasetotal > 0"
                :total="purchasetotal"
                :page.sync="purchaseQueryParams.pageNum"
                :limit.sync="purchaseQueryParams.pageSize"
                @pagination="loadPurchase"
              />
            </el-form>
          </el-col>

          <!-- 右侧表单 -->
          <el-col :span="12">
            <el-form
              ref="rightForm"
              :model="purchaseQueryDetailParams"
              size="small"
              label-width="100px"
              class="right-form"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                  <el-form-item label="物料编码">
                    <el-input v-model="purchaseQueryDetailParams.partCode" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="物料名称">
                    <el-input v-model="purchaseQueryDetailParams.partName" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handlePurchaseDetailQuery"
                      >搜索</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetPurchaseDetailQuery"
                      >重置</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <el-table
                :data="purchaseDetailList"
                @selection-change="handleSelectionDetail"
                ref="rightMultipleTable"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="物料编号" prop="partCode" />
                <el-table-column label="物料名称" prop="partName" />
              </el-table>
              <pagination
                v-show="purchaseDetailtotal > 0"
                :total="purchaseDetailtotal"
                :page.sync="purchaseQueryDetailParams.pageNum"
                :limit.sync="purchaseQueryDetailParams.pageSize"
                @pagination="loadPurchaseDetail"
              />
            </el-form>
          </el-col>
        </el-row>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="handleAddPurchaseDetail"
            >保 存</el-button
          >
          <el-button @click="handleDrawerClose">取 消</el-button>
        </div>
      </el-drawer>

      <!-- 明细添加物料对话框 -->
      <MaterialDialog
        :materialDialogVisible="materialDialogVisible"
        @updateDialogVisible="updateDialogVisible"
        @selectMateriaList="selectMateriaList"
      >
      </MaterialDialog>
    </div>
  </div>
</template>

<script>
import MaterialDialog from "../componet/materialDialog.vue";
import {
  listWms_sales,
  getWms_sales,
  delWms_sales,
  addWms_sales,
  updateWms_sales,
} from "@/api/system/wms_sales";

import { listMaterial } from "@/api/system/material";
import { listPurchase_detail } from "@/api/system/purchase_detail";
import { listPurchase, getPurchase } from "@/api/system/purchase";
import Outdetail from "@/views/stockOut/stockOutDetail/outdetail.vue";
import OutBox from "@/views/stockOut/stockOutBox/OutBox.vue";

export default {
  name: "Wms_sales",
  dicts: [
    "quality_state",
    "stock_out_state",
    "stock_out_type",
    "return_method",
  ],
  components: {
    MaterialDialog,
    Outdetail,
    OutBox, // 注册子组件
  },
  data() {
    return {
      isPurchaseOrderDisabled: false, // 控制采购订单框禁用状态
      activeNames: ["1", "2"],
      //详情
      activeNamesInfo: ["1", "2"],
      // 已存在的ID集合
      existingIds: new Set(),
      // 采购单列表
      purchaseList: [],
      purchasetotal: 0,
      purchaseLoading: false,
      // 采购单明细列表
      purchaseDetailList: [],
      purchaseDetailtotal: 0,
      purchaseDetailLoading: false,
      // 选中的采购单明细
      selectedPurchaseDetailList: [],
      // 选中项
      selectedItems: [],
      // 采购单抽屉标题
      purchaseTitle: "",
      // 采购单抽屉显示
      purchaseDrawerVisible: false,
      // 遮罩层
      loading: false,
      // 显示搜索与否
      showSearch: true,
      // 总条数
      total: 0,
      // 采购单查询参数
      purchaseQueryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierCode: "",
        supplierName: "",
        purchaseNo: "",
        purchaseNo: "",
      },
      // 采购单明细查询参数
      purchaseQueryDetailParams: {
        pageNum: 1,
        pageSize: 10,
        partCode: "",
        partName: "",
      },
      // 采购单表单数据
      // 表单参数
      form: {
        purchaseNo: "",
        purchaseId: null,
        supplierCode: "",
        supplierName: "",
        wmsStockOutDetailList: [],
      },
      //控制物料弹出框组件的属性
      materialDialogVisible: false,
      // 控制明细添加物料对话框
      dialogVisible: false,
      // 物料表格数据
      materialList: [],
      // 选中的物料数据
      detailmaterialList: [],

      isCompleted: false,
      detailTitle: "",
      openDetail: false,
      activeName: "first",
      stock_out_id: "",
      stock_detail_id: "",

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockOutDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销售退货表格数据
      wms_salesList: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockOutNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockOutType: "SALES_OUT",
        stockOutDate: null,
        stockOutStateArr: [],
        // stockOutState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockOutOntherType: null,
        comId: null,
        consignee: null,
      },

      // 表单参数
      form: { wmsStockOutDetailList: [] },

      // 表单校验
      rules: {
        // stockOutNo: [
        //   { required: true, message: "出库单号不能为空", trigger: "blur" },
        // ],
        stockOutType: [
          {
            required: true,
            message: "出库类型不能为空",
            trigger: "change",
          },
        ],
        stockOutDate: [
          { required: true, message: "出库日期不能为空", trigger: "change" },
        ],
        consignee: [
          { required: true, message: "收货方不能为空", trigger: "blur" },
        ],
        returnMethod: [
          { required: true, message: "退货方式不能为空", trigger: "change" },
        ],
        stockOutState: [
          {
            required: true,
            message: "出库状态不能为空",
            trigger: "change",
          },
        ],
        purchaseNo: [
          { required: true, message: "采购单号不能为空", trigger: "blur" },
        ],
       qty: [
          { required: true, message: "数量不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback();
              } else if (!/^[1-9]\d*$/.test(value)) {
                callback(new Error("请输入大于0的正整数"));
              } else {
                callback();
              }
            },
            trigger: ["blur", "change"]
          },
        ],
      },
    };
  },
  created() {
    console.log(this.form.wmsStockOutDetailList, "wmsStockOutDetailList");
    this.getList();
    this.loadPurchase();
    this.loadListMaterials();
    // this.loadPurchaseDetail();
  },
  methods: {
    // 数量输入校验
  handleQtyInput(row) {
    // 清除之前的错误状态
      row.qtyError = null;
       // 空值校验
      if (!row.qty || row.qty.trim() === '') {
        row.qtyError = '数量不能为空';
        return;
      }
      // 仅允许输入数字
      row.qty = row.qty.replace(/[^\d]/g, '');
      // 正则校验：大于0的整数
      const reg = /^[1-9]\d*$/;
      if (!reg.test(row.qty)) {
        row.qtyError = '数量必须为大于0的整数';
        return;
      }
      
      // 移除前导零
      if (row.qty.startsWith('0')) {
        row.qty = row.qty.replace(/^0+/, '') || '';
      }
      
      // 自动触发验证更新
      this.$nextTick(() => {
        this.$refs.form.validateField(`wmsStockOutDetailList.${this.form.wmsStockOutDetailList.indexOf(row)}.qty`);
      });
    },
    // 加载采购单列表
    loadPurchase() {
      this.purchaseLoading = true;
      listPurchase(this.purchaseQueryParams).then((response) => {
        this.purchaseList = response.rows;
        this.purchasetotal = response.total;
        this.purchaseLoading = false;
      });
    },

    // 加载采购单明细
    // loadPurchaseDetail() {
    //   if (!this.purchaseQueryParams.purchaseNo) {
    //     return;
    //   }

    //   this.purchaseDetailLoading = true;
    //   const params = {
    //     pageNum: this.purchaseQueryParams.pageNum,
    //     pageSize: this.purchaseQueryParams.pageSize,
    //     purchaseNo: this.purchaseQueryParams.purchaseNo,
    //     partCode: this.purchaseQueryDetailParams.partCode,
    //     partName: this.purchaseQueryDetailParams.partName,
    //   };

    //   listPurchase_detail(params)
    //     .then((response) => {
    //       this.purchaseDetailList = response.rows;
    //       this.purchaseDetailtotal = response.total;
    //     })
    //     .catch((error) => {
    //       this.$modal.msgError("加载采购单明细失败: " + error);
    //     })
    //     .finally(() => {
    //       this.purchaseDetailLoading = false;
    //     });
    // },
    // 加载采购单明细
    loadPurchaseDetail() {
      this.purchaseDetailLoading = true;
      listPurchase_detail(this.purchaseQueryParams).then((response) => {
        this.purchaseDetailList = response.rows;
        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    // 采购单查询
    handlePurchaseQuery() {
      this.purchaseQueryParams.pageNum = 1;
      this.purchaseQueryParams.pageNum = 10;
      this.loadPurchase();
    },

    // 重置采购单查询
    resetPurchaseQuery() {
      this.purchaseQueryParams = {
        pageNum: 1,
        pageSize: 10,
        supplierCode: "",
        supplierName: "",
        purchaseNo: "",
        purchaseNo: "",
      };
      this.loadPurchase();
    },

    // 重置采购单明细查询
    resetPurchaseDetailQuery() {
      this.purchaseQueryDetailParams = {
        pageNum: 1,
        pageSize: 10,
        partCode: "",
        partName: "",
      };
      this.loadPurchaseDetail();
    },

    // 采购单选择处理
    handleSelectionPurchase(row, column, event) {
      // 判断是否已选中
      if (!row) return;

      // 根据点击row更新查询参数和表单数据
      this.purchaseQueryParams = {
        ...this.purchaseQueryParams,
        purchaseNo: row.purchaseNo,
        purchaseNo: row.id,
        supplierCode: row.supplierCode,
        supplierName: row.supplierName,
      };

      // 更新采购单表单数据
      this.form = {
        purchaseNo: row.purchaseNo,
        purchaseNo: row.id,
        supplierCode: row.supplierCode,
        supplierName: row.supplierName,
      };

      // 加载对应的采购明细
      this.loadPurchaseDetail();
    },

    // 多选框选中数据
    handleSelectionChangePurchase(selection) {
      let purchaseNo;
      let newArr = [];
      if (selection.length > 1) {
        var newRows = selection.filter((it, index) => {
          if (index == selection.length - 1) {
            this.$refs.leftMultipleTable.toggleRowSelection(it, true); //这行可以不要
            return true;
          } else {
            this.$refs.leftMultipleTable.toggleRowSelection(it, false);
            return false;
          }
        });
        newArr = newRows;
        purchaseNo = newRows[0].id;
      } else {
        newArr = selection;
        purchaseNo = selection[0].id;
      }
      this.form.purchaseNo = newArr[0].purchaseNo;
      // this.form.purchaseId = newArr[0].id;
      this.form.supplierId = newArr[0].supplierId;
      this.form.supplierCode = newArr[0].supplierCode;
      this.form.supplierName = newArr[0].supplierName;
      // 使用purchaseNo查询
      const params = {
        purchaseNo: this.form.purchaseNo,
      };

      listPurchase_detail(params).then((response) => {
        this.purchaseDetailList = response.rows;

        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    rowClick(row) {
      this.$refs.leftMultipleTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    onSelectAll() {
      this.$refs.leftMultipleTable.clearSelection();
    },

    // 明细多选变更
    handleSelectionDetail(selection) {
      this.selectedItems = selection;
      this.selectedPurchaseDetailList = selection;
    },

    // 确认添加采购明细
    handleAddPurchaseDetail() {
      // 这里根据输入去判断是否为空，如果有数据就是修改
      if (this.purchaseInputValue !== "") {
        console.log("purchaseInputValue:", this.purchaseInputValue);
        this.form.wmsStockOutDetailList = [];
        console.log(
          "去修改的时候先清空this.form.wmsStockOutDetailList:",
          this.form.wmsStockOutDetailList
        );
      }

      this.selectedPurchaseDetailList.forEach((obj) => {
        if (!this.existingIds.has(obj.id)) {
          // const detail = {
          //   materialId: obj.id,
          //   materialCode: obj.partCode,
          //   materialName: obj.partName,
          //   specification: obj.partSpecification,
          //   materialUnit: obj.uom,
          //   qty: 1,
          //   stockOutState: "TO_BE_CREATED",
          // };
          // 赋值字段
          // 这里用obj，直接赋值push不用去再去定义变量赋值，后面删除的也是obj.id,
          obj.materialId = obj.id;
          obj.materialCode = obj.partCode;
          obj.materialName = obj.partName;
          obj.specification = obj.partSpecification;
          obj.materialUnit = obj.uom;
          console.log(obj, "obj---------");
          this.form.wmsStockOutDetailList.push(obj);
          console.log(
            this.form.wmsStockOutDetailList,
            "this.form.wmsStockOutDetailList"
          );
          // 标记该 id 已存在
          this.existingIds.add(obj.id);
          // 去disabled采购订单框
          this.isPurchaseOrderDisabled =
            this.form.wmsStockOutDetailList.length > 0;

          this.$refs?.leftMultipleTable?.clearSelection();
          this.$refs?.rightMultipleTable?.clearSelection();
        }
      });
      this.purchaseDrawerVisible = false;
      console.log(this.purchaseDrawerVisible);
    },

    // 打开采购单选择
    importFuction(event) {
      this.purchaseDetailList = [];
      this.purchaseDetailtotal = 0;

      // this.purchaseQueryDetailParams = {};
      this.purchaseInputValue = event.target.value; // 直接获取输入框的当前值
      console.log("purchaseInputValue输入框的值:", this.purchaseInputValue);

      event.target.blur(); // 输入框失焦后再打开抽屉
      this.purchaseDrawerVisible = true;
      this.purchaseTitle = "选择采购订单";
    },

    // 关闭采购单选择
    handleDrawerClose() {
      // 重置一下左边勾选
      this.$refs?.leftMultipleTable?.clearSelection();
      this.purchaseDrawerVisible = false;
      this.$refs.purchaseInput?.blur(); // 主动触发失焦
    },

    //明细push子组件传过来的数据（选中的物料）
    selectMateriaList(arrs) {
      this.form.wmsStockOutDetailList.push(...arrs);
      this.updateDialogVisible(); //这个不用动，是关子组件弹窗的方法
    },
    //让父组件关闭子组件的弹出框 也不用动
    updateDialogVisible() {
      this.materialDialogVisible = false;
    },
    /** 查询销售退货列表 */
    getList() {
      this.loading = true;
      listWms_sales(this.queryParams).then((response) => {
        this.wms_salesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      // 取消的时候，清除保存的existingIds
      this.existingIds = new Set();
      console.log("取消后的 existingIds:", this.existingIds);
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockOutNo: null,
        purchaseNo: null,
        purchaseId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockOutType: "SALES_OUT",
        stockOutDate: null,
        stockOutState: "TO_BE_CREATED",
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockOutOntherType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        wmsStockOutDetailList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handlePurchaseDetailQuery() {
      // 物料搜索点击
      this.purchaseQueryDetailParams.pageNum = 1;
      listPurchase_detail(this.purchaseQueryDetailParams).then((response) => {
        this.purchaseDetailList = response.rows;
        console.log("物料搜索点击", this.purchaseDetailList);
        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleSelectionChangematerial(selection) {
      this.ids = selection.map((item) => item.id);
      console.log("选中的ID列表：", this.ids);
      // 获取选中项的ID数组

      // 根据ID筛选需要提交的数据
      this.detailmaterialList = this.materialList.filter((item) =>
        this.ids.includes(item.id)
      );
      console.log("筛选后的提交数据：", this.detailmaterialList);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isPurchaseOrderDisabled = false;
      this.$refs.leftMultipleTable.clearSelection();
      this.$refs.rightMultipleTable.clearSelection();
      this.title = "新增销售发货单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // 让输入框能够输入
      this.isPurchaseOrderDisabled = false;
      // 修改的时候先去清除保存的existingIds
      this.existingIds = new Set();
      console.log("修改清空的 existingIds:", this.existingIds);

      this.reset();
      const id = row.id || this.ids;
      getWms_sales(id).then((response) => {
        this.form = response.data;
        this.form.wmsStockOutDetailList = response.data.wmsStockOutDetailList;
        this.open = true;
        this.title = "修改销售发货";
      });
    },
    // 录入完成
    handlecomplete(row) {
      this.$modal
        .confirm('是否确认录入编号为"' + row.stockOutNo + '"的数据项？')
        .then(function () {})
        .then(() => {
          getWms_sales(row.id).then((response) => {
            this.form = response.data;
            this.form.stockOutState = "STOCK_OUT_PENDING";
            updateWms_sales(this.form).then((response) => {
              this.$modal.msgSuccess("录入完成");
              this.getList();
              // this.open = false;
            });
          });
        })
        .catch(() => {});
    },
    /**查看详情 */
    handleDatail(row) {
      console.log("看看", row);

      this.reset();
      this.activeName = "first";
      this.stock_out_id = row.id;
      const id = row.id || this.ids;
      getWms_sales(id).then((response) => {
        this.form = response.data;
        this.form.wmsStockOutDetailList = response.data.wmsStockOutDetailList;
        this.openDetail = true;
        this.detailTitle = "销售发货单详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 新增校验：检查明细表是否有数据
          if (
            !this.form.wmsStockOutDetailList ||
            this.form.wmsStockOutDetailList.length === 0
          ) {
            this.$message.error("请至少添加一条物料明细");
            return;
          }
          if (this.form.id != null) {
            updateWms_sales(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWms_sales(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 作废按钮操作 */
    handlecancellation(row) {
      this.$modal
        .confirm('是否确认作废编号为"' + row.stockOutNo + '"的数据项？')
        .then(function () {})
        .then(() => {
          getWms_sales(row.id).then((response) => {
            this.form = response.data;
            this.form.stockOutState = "TO_BE_CANCELLATION";
            updateWms_sales(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.getList();
            });
          });
        })
        .catch(() => {});
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除销售退货编号为"' + ids + '"的数据项？')
        .then(function () {
          return delWms_sales(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 出库单明细序号 */
    rowWmsStockOutDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    loadListMaterials() {
      listMaterial(this.queryParams).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        console.log("this.materialList", this.materialList);
      });
    },
    handleAddMaterial() {
      this.detailmaterialList.forEach((obj) => {
        obj.partId = obj.id;
        console.log("obj", obj);

        this.form.wmsStockOutDetailList.push(obj);
      });
      console.log(
        "this.form.wmsStockOutDetailList",
        this.form.wmsStockOutDetailList
      );

      this.dialogVisible = false;
    },
    /** 出库单明细添加按钮操作 */
    handleAddWmsStockOutDetail() {
      this.materialDialogVisible = true;
    },

    /** 出库单明细删除按钮操作 */
    handleDeleteWmsStockOutDetail() {
      if (this.checkedWmsStockOutDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的出库单明细数据");
      } else {
        const wmsStockOutDetailList = this.form.wmsStockOutDetailList;
        const checkedWmsStockOutDetail = this.checkedWmsStockOutDetail;
        // 1. 收集被删除项的 ID（根据选中的 index）
        const deletedIds = checkedWmsStockOutDetail
          .map((index) => wmsStockOutDetailList[index - 1]?.id) // 根据 index 获取 id index的要从0开始，所以这里要-1！！
          .filter((id) => id !== undefined); // 过滤无效值
        console.log("被删除的 ID:", deletedIds);

        this.form.wmsStockOutDetailList = wmsStockOutDetailList.filter(
          function (item) {
            return checkedWmsStockOutDetail.indexOf(item.index) == -1;
          }
        );
        // 这里是Set
        deletedIds.forEach((id) => this.existingIds.delete(id));

        console.log("更新后的 existingIds:", this.existingIds);
        // 清空选中状态
        this.checkedWmsStockOutDetail = [];
        // 去判断是否数据等于0条，然后去取消disabled采购订单框
        if (this.form.wmsStockOutDetailList.length === 0) {
          this.isPurchaseOrderDisabled = false; // 可选重置
        }
      }
    },
    /** 复选框选中数据 */
    handleWmsStockOutDetailSelectionChange(selection) {
      this.checkedWmsStockOutDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/wms_sales/export",
        {
          ...this.queryParams,
        },
        `销售退货_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    receiveDetailData(data) {
      // console.log("看看", data);
      this.stock_detail_id = data;
    },
    // 标签点击
    handleClick() {
      this.stock_detail_id = null;
    },
  },
};
</script>
<style scoped>
/* 栅格布局调整 */
.el-row {
  height: 50%;
}

/* 左侧表单样式 */
.left-form {
  height: 100%;
  overflow-y: auto;
  /* padding-right: 10px; */
}

/* 右侧表单样式 */
.right-form {
  height: 100%;
  overflow-y: auto;
  /* padding-left: 10px; */
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 12px;
}
/* 添加样式确保错误信息完整显示 */
.qty-form-item {
  margin-bottom: 30px; /* 为错误信息留出足够空间 */
}

/* 确保错误信息完整显示 */
.qty-form-item {
  margin-bottom: 30px;
}

/* 调整错误信息样式 */
.qty-form-item >>> .el-form-item__error {
  position: static; /* 确保错误信息显示在正确位置 */
  padding-top: 2px;
  font-size: 12px;
  line-height: 1.5;
  white-space: nowrap;
  color: #f56c6c;
}
/* 错误提示样式 */
.error-tip {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: relative;
  left: 0;
  top: -5px;
}
</style>