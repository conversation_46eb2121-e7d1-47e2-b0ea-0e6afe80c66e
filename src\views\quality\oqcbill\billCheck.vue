<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-row>
        <el-col :span="12">
          <div>
            <span class="custom_title">
              <svg-icon icon-class="block" />
              <div class="custom-div">基础信息</div>
            </span>
            <el-form ref="form" :model="form">
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="质检单号"
                    prop="iqcNo"
                    style="width: 240px"
                  >
                    {{ form.iqcNo }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="严格度"
                    prop="adjustedSeverity"
                    style="width: 240px"
                  >
                    <dict-tag
                      :options="dict.type.iqc_insp_config_strictness"
                      :value="form.adjustedSeverity"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="出库单号"
                    prop="stockOutNo"
                    style="width: 240px"
                  >
                    {{ form.stockInNo }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="收货单号"
                    prop="receiveNo"
                    style="width: 240px"
                  >
                    {{ form.receiveNo }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="采购单号"
                    prop="purchaseNo"
                    style="width: 240px"
                  >
                    {{ form.purchaseNo }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="物料编码"
                    prop="materialCode"
                    style="width: 240px"
                  >
                    {{ form.materialCode }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="供应商"
                    prop="supplierName"
                    style="width: 240px"
                  >
                    {{ form.supplierCode + "-" + form.supplierName }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="物料名称"
                    prop="materialName"
                    style="width: 240px"
                  >
                    {{ form.materialName }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="数量" prop="qty" style="width: 240px">
                    {{ form.qty }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="批次"
                    prop="batchNo"
                    style="width: 240px"
                  >
                    {{ form.batchNo }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="质检状态"
                    prop="billStatus"
                    style="width: 240px"
                  >
                    <dict-tag
                      :options="dict.type.bill_status"
                      :value="form.billStatus"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="质检结果"
                    prop="inspResult"
                    style="width: 240px"
                  >
                    <dict-tag
                      :options="dict.type.insp_result"
                      :value="form.inspResult"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-form-item
                label="检验维度；字典inspection_from"
                prop="inspectionFrom"
                style="width: 240px"
              >
                <el-input
                  v-model="form.inspectionFrom"
                  placeholder="请输入检验维度；字典inspection_from"
                />
              </el-form-item> -->
            </el-form>
          </div>
          <div>
            <span class="custom_title">
              <svg-icon icon-class="block" />
              <div class="custom-div">检验项列表</div>
            </span>
            <el-table
              height="52vh"
              v-loading="loading"
              :data="billItemResultList"
              highlight-current-row
              @current-change="handleCurrentChange"
            >
              <el-table-column
                label="项目编码"
                align="center"
                prop="itemCode"
              />
              <el-table-column
                label="项目名称"
                align="center"
                prop="itemName"
              />
              <el-table-column
                label="仪器"
                align="center"
                prop="instrumentName"
              />
              <el-table-column
                label="检验方法"
                align="center"
                prop="inspectionMethod"
              />
              <el-table-column label="样品数" align="center" prop="sampleNum" />
              <el-table-column
                label="分析方法"
                align="center"
                prop="analysisMethod"
              >
                <template slot-scope="scope">
                  <dict-tag
                    :options="dict.type.analysis_method"
                    :value="scope.row.analysisMethod"
                  />
                </template>
              </el-table-column>
              <el-table-column label="单位" align="center" prop="unit" />
              <el-table-column label="不良数" align="center" prop="qtyDefect" />

              <el-table-column
                label="测量结果"
                align="center"
                prop="judgeResult"
              >
                <template slot-scope="scope">
                  <dict-tag
                    :options="dict.type.judge_result"
                    :value="scope.row.judgeResult"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="app-container-top" style="">
            <div class="app-container-bottom">
              <el-button size="small" @click="goBack">取 消</el-button>
              <el-button
                size="small"
                type="primary"
                @click="workArea"
                v-if="form.status == 'update'"
                >驳 回</el-button
              >
              <el-button
                size="small"
                type="primary"
                @click="firstSubmit"
                v-if="form.status == 'update'"
                >审 核</el-button
              >
            </div>
          </div>
          <span class="custom_title">
            <svg-icon icon-class="block" />
            <div class="custom-div">检验产品列表</div>
          </span>
          <el-table height="50vh" v-loading="loading" :data="billitemList">
            <el-table-column label="下限值" align="center" prop="lowerLimit" />
            <el-table-column label="测量值" align="center" prop="measuredValue">
            </el-table-column>
            <el-table-column label="上限值" align="center" prop="upperLimit" />

            <!-- <el-table-column label="单位" align="center" prop="itemUnit" />
            <el-table-column
              label="是否合格"
              align="center"
              prop="qualitativeResults"
            /> -->
            <el-table-column label="判断结果" align="center" prop="judgeResult">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.judge_result"
                  :value="scope.row.judgeResult"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// import {
//   listBill,
//   getBill,
//   delBill,
//   addBill,
//   updateBill,
// } from "@/api/quality/bill";
import {
  listOqcbill,
  getOqcbill,
  delOqcbill,
  addOqcbill,
  updateOqcbill,
} from "@/api/quality/oqcbill";

// import { listBillitem } from "@/api/quality/billitem";
import { listOqcitem } from "@/api/quality/oqcitem";

// import { listResult } from "@/api/quality/result";
import { listOqcresult } from "@/api/quality/oqcresult";

export default {
  name: "BillCheck",
  dicts: [
    "bill_status",
    "iqc_insp_config_strictness",
    "insp_result",
    "inspection_from",
    "inspection_from",
    "insp_type",
    "judge_result",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // iqc质检单据表格数据
      billList: [],
      // iqc质检单据表格数据
      billItemResultList: [],
      //iqc质检明细
      billitemList: [],
      // 质检单号
      iqcNos: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        iqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        comId: null,
      },
      // 表单参数
      form: {},
      currentRow: null,
    };
  },

  beforeRouteEnter(to, from, next) {
    if (to.query.title) {
      to.meta.title = to.query.title;
    }
    next();
  },
  created() {
    if (this.$route.query) {
      this.form = this.$route.query;
      //   this.$route.meta.title = this.$route.query.title;
    }
    this.queryParams.billId = this.form.id;
    this.getBillItemResultList();
  },
  methods: {
    /** 查询iqc检检测项目列表 */
    getBillItemResultList() {
      this.loading = true;
      listOqcresult(this.queryParams).then((response) => {
        // for (let i = 0; i < response.rows.length; i++) {
        //   this.getBillItemList(response.rows[i]);
        // }
        console.log(response.rows, "response.rows");
        this.billItemResultList = response.rows;
        console.log(this.billItemResultList, "this.billItemResultList");

        this.total = response.total;
        this.loading = false;
      });
    },
    getBillItemList(row) {
      const params = {
        inspBillId: this.form.id,
        itemCode: row.itemCode,
        pageNum: 1,
        pageSize: 10000,
      };
      listOqcitem(params).then((res) => {
        if (res.rows.length > 0) {
          this.billitemList = res.rows;
        }
      });
    },
    handleCurrentChange(row) {
      this.currentRow = row;
      this.getBillItemList(row);
    },
    goBack() {
      // 关闭当前标签页并返回上个页面
      const obj = {
        path: "/quality/oqcitem",
        query: { t: Date.now() },
      };
      this.$tab.closeOpenPage(obj);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        iqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.iqcNos = selection.map((item) => item.iqcNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加iqc质检单据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getOqcbill(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改iqc质检单据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateOqcbill(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOqcbill(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const iqcNos = row.iqcNo || this.iqcNos;
      console.log("iqcNos", row.iqcNo);
      this.$modal
        .confirm('是否确认删除iqc质检单据编号为"' + iqcNos + '"的数据项？')
        .then(function () {
          return delOqcbill(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/bill/export",
        {
          ...this.queryParams,
        },
        `iqc质检单据_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    //驳回
    workArea() {
      this.$confirm("是否驳回该任务?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //审核驳回
          this.form.billStatus = "REJECT";
          updateOqcbill(this.form).then((response) => {
            this.$modal.msgSuccess("驳回成功");
            this.goBack();
          });
        })
        .catch(() => {
          console.log("已取消驳回");
        });
    },
    //审核提交
    firstSubmit() {
      this.$confirm("是否审核该任务?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //已审核
          this.form.billStatus = "COMPLETED";
          updateOqcbill(this.form).then((response) => {
            this.$modal.msgSuccess("审核成功");
            this.goBack();
          });
        })
        .catch(() => {
          console.log("已取消审核");
        });
    },
  },
};
</script>
