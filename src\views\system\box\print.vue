<template>
  <div class="app-container">
    <div class="app-container-div">
      <!-- 添加或修改箱信息对话框 -->

      <el-form ref="form" :model="form" :rules="rules">
        <el-row>
          <el-col :span="6">
            <el-form-item label="采购单号" prop="purchaseNo" style="width: 260px">
              <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" @focus="changePurchase" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料编码" prop="materialCode" style="width: 260px">
              <el-input v-model="form.materialCode" placeholder="请输入物料编码" @focus="changePurchaseDetail" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料名称" prop="materialName" style="width: 260px">
              <el-input v-model="form.materialName" placeholder="请输入物料名称" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次" prop="batchNo" style="width: 260px">
              <el-input v-model="form.batchNo" placeholder="请输入批次" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item label="供应商编码" prop="supplierCode" style="width: 260px">
              <el-input v-model="form.supplierCode" placeholder="请输入供应商编码" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="供应商名称" prop="supplierName" style="width: 260px">
              <el-input v-model="form.supplierName" placeholder="请输入供应商名称" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单分类" prop="orderClass" style="width: 260px">
              <el-select v-model="form.orderClass" placeholder="请选择订单状态" clearable style="width: 260px">
                <el-option v-for="dict in dict.type.order_class" :key="dict.id" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="保质期" prop="expirationDate" style="width: 260px">
              <el-date-picker clearable v-model="form.expirationDate" type="date" value-format="yyyy-MM-dd"
                placeholder="请选择保质期" style="width: 260px" :picker-options="pickerEndTime">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item label="生产日期" prop="dateCode" style="width: 260px">
              <el-date-picker clearable v-model="form.dateCode" type="date" value-format="yyyy-MM-dd"
                placeholder="请选择生产日期" style="width: 260px" :picker-options="pickerStartTime">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数量" prop="qty" style="width: 260px">
              <!-- <el-input v-model="form.qty" placeholder="请输入数量" /> -->
              <el-input-number v-model="form.qty" :min="1" label="请输入数量" style="width: 260px"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="打印数量" prop="printQty" style="width: 260px">
              <el-input-number v-model="form.printQty" :min="1" label="请输入打印数量" style="width: 260px"></el-input-number>
              <!-- <el-input v-model="form.printQty" placeholder="请输入打印数量" /> -->
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="打印模板" prop="printQty" style="width: 260px">
              <el-select style="width: 260px" v-model="form.printId" placeholder="请选择打印模板" clearable disabled>
                <el-option v-for="item in hiprintList" :key="item.id" :label="item.code + '-' + item.name"
                  :value="item.id" />
              </el-select>
              <!-- <el-input v-model="form.printQty" placeholder="请输入打印数量" /> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="规格型号" prop="specification" style="width: 260px">
              <el-input disabled v-model="form.specification" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单位" prop="materialUnit" style="width: 260px">
              <el-input v-model="form.materialUnit" placeholder="请输入单位" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态" prop="boxState" style="width: 260px">
              <el-select v-model="form.boxState" placeholder="请选择状态" clearable style="width: 260px" disabled>
                <el-option v-for="dict in dict.type.box_state" :key="dict.id" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6"> </el-col>
        </el-row>

        <!-- <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item> -->
      </el-form>
      <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
      <!-- 采购订单主 -->
      <el-dialog title="选择采购单" :visible.sync="dialogVisible" width="80%">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
          label-width="120px">
          <el-form-item label="采购单号" prop="purchaseNo">
            <el-input v-model="queryParams.purchaseNo" placeholder="请输入采购单号" clearable />
          </el-form-item>
          <el-form-item label="供应商名称" prop="supplierName">
            <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getPurchaseList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <!-- 采购订单列表 -->
        <el-table style="cursor: pointer" height="40vh" @row-click="rowClick" v-loading="loading" :data="purchaseList"
          @selection-change="handleSelectionChange" @select-all="onSelectAll" ref="multipleTable">
          
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="采购单号" align="center" prop="purchaseNo" />
          <el-table-column label="供应商编码" align="center" prop="supplierCode" />
          <el-table-column label="供应商名称" align="center" prop="supplierName" />
          <el-table-column label="行状态" align="center" prop="lineState">
            <template slot-scope="scope">
              <dict-tag type="success" :options="dict.type.line_state_dict" :value="scope.row.lineState" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getPurchaseList" />

        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="purchaseSubmit">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 采购订单明细 -->
      <el-dialog title="选择物料" :visible.sync="detaildialogVisible" width="80%">
        <el-form :model="queryParamsDeatil" ref="queryFormdetail" size="small" :inline="true" v-show="showSearch"
          label-width="120px">
          <el-form-item label="采购单号" prop="purchaseNo">
            <el-input v-model="queryParamsDeatil.purchaseNo" placeholder="请输入采购单号" clearable />
          </el-form-item>
          <el-form-item label="供应商名称" prop="supplierName">
            <el-input v-model="queryParamsDeatil.supplierName" placeholder="请输入供应商名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getPurchaseList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        

        <!-- 采购订单明细列表 -->
        <el-table style="cursor: pointer" height="40vh" v-loading="detailloading" :data="wmsErpPurchaseDetailList"
          @row-click="rowDetailClick" @selection-change="handleSelectionDetail" @select-all="onSelectDetailAll"
          ref="detailTable">
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column
            label="采购单号"
            align="center"
            prop="purchaseNo"
          /> -->
          <el-table-column label="物料编码" align="center" prop="partCode" />
          <el-table-column label="物料名称" align="center" prop="partName" />
          <el-table-column label="数量" align="center" prop="qty" />
          <el-table-column label="单位" align="center" prop="uom" />
        </el-table>
        <pagination v-show="detailtotal > 0" :total="detailtotal" :page.sync="queryParamsDeatil.pageNum"
          :limit.sync="queryParamsDeatil.pageSize" @pagination="detail_list" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="detaildialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="purchaseDetailSubmit">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listHiprint } from "@/api/system/hiprint";
import { listBox, getBox, delBox, addBox, updateBox } from "@/api/system/box";
import { listPurchase_detail } from "@/api/system/purchase_detail";
import { listPurchase } from "@/api/system/purchase";
import { genCode } from "@/api/autocode/rule";
import { Loading } from "element-ui";
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";

export default {
  dicts: ["box_state", "order_class", "line_state_dict"],
  name: "Box",
  data() {
    return {
      showMoreConditions: false, // 控制是否显示更多条件

      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,

      // 箱信息表格数据
      boxList: [],
      // 采购单表格数据
      purchaseList: [],
      // 遮罩层
      loading: true,
      dialogVisible: false,
      //选择框选中数据
      selectPurchaselist: [],
      // 总条数
      total: 0,
      detailtotal:0,
      // 采购单明细表格数据
      wmsErpPurchaseDetailList: [],
      // 遮罩层
      detailloading: true,
      detaildialogVisible: false,
      //选择框选中数据
      selectPurchaseDetaillist: [],
      // 总条数
      detailtotal: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        batchNo: null,
        comId: null,
        orderClass: null,
      },
      queryParamsDeatil: {
        materialCode: null,
        materialName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseNo: [
          { required: true, message: "采购单号不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        printQty: [
          { required: true, message: "打印数量不能为空", trigger: "blur" },
        ],
        supplierCode: [
          { required: true, message: "供应商不能为空", trigger: "blur" },
        ],
        batchNo: [{ required: true, message: "批次不能为空", trigger: "blur" }],
        dateCode: [
          { required: true, message: "生产日期不能为空", trigger: "blur" },
        ],
        expirationDate: [
          { required: true, message: "保质期不能为空", trigger: "blur" },
        ],
      },
      mypanel: {},
      hiprintList: [],
    };
  },
  created() {
    this.reset();
    this.getList();
    this.getPrintList();
  },
  computed: {
    pickerStartTime() {
      let _this = this;

      return {
        disabledDate: (time) => {
          if (_this.form.expirationDate) {
            let expirationDate = _this.form.expirationDate.replace(/-/g, "/");

            return time.getTime() >= new Date(expirationDate);
          }
        },
      };
    },

    pickerEndTime() {
      let _this = this;

      return {
        disabledDate: (time) => {
          if (_this.form.dateCode) {
            let dateCode = _this.form.dateCode.replace(/-/g, "/");

            return time.getTime() <= new Date(dateCode);
          }
        },
      };
    },
  },
  methods: {
    getPrintList() {
      this.loading = true;
      listHiprint({ code: "box" }).then((response) => {
        this.form.printId = response.rows[0].id;
        this.mypanel = JSON.parse(response.rows[0].printJson);
        this.hiprintList = response.rows;
      });
    },
    toggleMoreConditions() {
      this.showMoreConditions = !this.showMoreConditions; // 切换显示状态
    },
    /** 查询箱信息列表 */
    getList() {
      this.loading = true;
      listBox(this.queryParams).then((response) => {
        this.boxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: "CREATED",
        batchNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        orderClass: "mass",
        printQty: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      console.log("表单", this.queryParams);

      this.resetForm("queryForm");
      this.getPurchaseList();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加箱信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBox(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改箱信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let loadingInstance = Loading.service({
            fullscreen: true,
            target: document.querySelector(".el-main"),
            text: "打印中...",
            background: "rgba(0, 0, 0, 0.8)",
          });
          addBox(this.form).then((response) => {
            this.$modal.msgSuccess("打印成功");
            for (let i = 0; i < response.data.length; i++) {
              const item = response.data[i];
              console.log(item, "item--")
              this.$nextTick(() => {
                // 以服务的方式调用的 Loading 需要异步关闭
                loadingInstance.close();

                //进行pdf打印

                hiprint.init();
                //调用接口获取数据
                var hiprintTemplate = new hiprint.PrintTemplate({
                  template: this.mypanel,
                  settingContainer: "#templateDesignDiv",
                });
                this.form.qrCode = item;
                this.form.boxNo = item;
                hiprintTemplate.print([this.form]);
              });
            }
          });
        }
        this.reset();
      });
      
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除箱信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delBox(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/box/export",
        {
          ...this.queryParams,
        },
        `箱信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },

    // 采购单开始
    //采购单获取焦点，打开采购单列表
    changePurchase() {
      this.getPurchaseList();
      this.loading = false;
      this.dialogVisible = true;
    },
    getPurchaseList() {
      listPurchase(this.queryParams).then((response) => {
        console.log("查询采购单列表:", response.rows);
        this.purchaseList = response.rows;
        this.total = response.total;
      });
    },
    //某一行被点击的时候
    purchaseSubmit() {
      if (this.selectPurchaselist.length == 0) {
        this.$message({
          message: "请选择数据",
          type: "warning",
        });
        return;
      }
      let row = this.selectPurchaselist[0];
      this.dialogVisible = false;
      this.form.purchaseNo = row.purchaseNo;
      this.form.purchaseNo = row.purchaseNo;
      this.form.supplierCode = row.supplierCode;
      this.form.supplierName = row.supplierName;
      this.form.supplierId = row.supplierId;
      //生成批次号
      genCode("box_batch_no").then((response) => {
        this.form.batchNo = response;
      });
      console.log("被电击了", row, this.selectPurchaselist);
    },
    rowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection);
      if (selection.length > 1) {
        var newRows = selection.filter((it, index) => {
          if (index == selection.length - 1) {
            this.$refs.multipleTable.toggleRowSelection(it, true); //这行可以不要
            return true;
          } else {
            this.$refs.multipleTable.toggleRowSelection(it, false);
            return false;
          }
        });
        this.selectPurchaselist = newRows;
      } else {
        this.selectPurchaselist = selection;
      }
      console.log(this.selectPurchaselist, "this.selectPurchaselist");
    },
    onSelectAll() {
      this.$refs.multipleTable.clearSelection();
    },
    // 采购单结束

    // 采购单明细开始
    //打开采购单明细列表
    changePurchaseDetail() {
      if (!this.form.purchaseNo) {
        this.$message({
          message: "请先选择采购单",
          type: "warning",
        });
        return;
      }
      this.detail_list();
      this.detailloading = false;
      this.detaildialogVisible = true;
    },
    //某一行被点击的时候
    purchaseDetailSubmit() {
      if (this.selectPurchaseDetaillist.length == 0) {
        this.$message({
          message: "请选择数据",
          type: "warning",
        });
        return;
      }
      let row = this.selectPurchaseDetaillist[0];
      this.detaildialogVisible = false;
      this.form.materialCode = row.partCode;
      this.form.materialName = row.partName;
      this.form.materialId = row.partId;
      this.form.materialUnit = row.uom;
      this.form.specification = row.partSpecification;
      console.log("被电击了", row, this.selectPurchaseDetaillist);
    },
    rowDetailClick(row) {
      this.$refs.detailTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    // 多选框选中数据
    handleSelectionDetail(selection) {
      console.log(selection);
      if (selection.length > 1) {
        var newRows = selection.filter((it, index) => {
          if (index == selection.length - 1) {
            this.$refs.detailTable.toggleRowSelection(it, true); //这行可以不要
            return true;
          } else {
            this.$refs.detailTable.toggleRowSelection(it, false);
            return false;
          }
        });
        this.selectPurchaseDetaillist = newRows;
      } else {
        this.selectPurchaseDetaillist = selection;
      }
      console.log(
        this.selectPurchaseDetaillist,
        "this.selectPurchaseDetaillist"
      );
    },
    onSelectDetailAll() {
      this.$refs.detailTable.clearSelection();
    },

    /** 查询采购单明细列表 */
    detail_list() {
      let queryParams = {
        pageNum: 1,
        pageSize: 10,
        purchaseNo: this.form.purchaseNo,
      };
      listPurchase_detail(queryParams).then((response) => {
        this.wmsErpPurchaseDetailList = response.rows;
        this.detailtotal = response.total;
        console.log("结果", response);
      });
    },

    //供应商选择改变的时候
    supplierNameChange(item) {
      console.log("item:", item);
      this.form.supplierCode = item.supplierCode;
      this.form.supplierName = item.supplierName;
    },
    /** 查询 供应商管理列表 */
    getSupplierList() {
      listSupplier(this.queryParams).then((response) => {
        this.supplierList = response.rows;
        console.log("rows:", this.supplierList);
      });
    },
  },
};
</script>
