<template>
  <div class="app-container">
    <div class="app-container-div">
      <div class="app-container-top" style="">
        <div class="app-container-bottom">
          <el-button size="small" @click="goBack">取 消</el-button>
          <el-button
            size="small"
            type="primary"
            @click="workArea"
            v-if="form.status == 'update'"
            >暂 存</el-button
          >
          <el-button
            size="small"
            type="primary"
            @click="firstSubmit"
            v-if="form.status == 'update'"
            >提 交</el-button
          >
        </div>
      </div>

      <span class="custom_title">
        <svg-icon icon-class="block" />
        <div class="custom-div">物料基础信息11</div>
      </span>
      <el-form ref="form" :model="form" :rules="rules">
        <el-row>
          <el-col :span="6">
            <el-form-item label="质检单号" prop="oqcNo" style="width: 240px">
              <el-input
                disabled
                v-model="form.oqcNo"
                placeholder="请输入质检单号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="物料编码"
              prop="materialCode"
              style="width: 240px"
            >
              <el-input
                disabled
                v-model="form.materialCode"
                placeholder="请输入物料编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="物料名称"
              prop="materialName"
              style="width: 240px"
            >
              <el-input
                disabled
                v-model="form.materialName"
                placeholder="请输入物料名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="供应商编码"
              prop="supplierCode"
              style="width: 240px"
            >
              <el-input
                disabled
                v-model="form.supplierCode"
                placeholder="请输入供应商编码"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item
              label="供应商名称"
              prop="supplierName"
              style="width: 240px"
            >
              <el-input
                disabled
                v-model="form.supplierName"
                placeholder="请输入供应商名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次" prop="batchNo" style="width: 240px">
              <el-input
                disabled
                v-model="form.batchNo"
                placeholder="请输入批次"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="采购单号"
              prop="purchaseNo"
              style="width: 240px"
            >
              <el-input
                disabled
                v-model="form.purchaseNo"
                placeholder="请输入采购单号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货数量" prop="qty" style="width: 240px">
              <el-input disabled v-model="form.qty" placeholder="请输入数量" />
            </el-form-item>
          </el-col>
        </el-row>
        <span class="custom_title">
          <svg-icon icon-class="block" />
          <div class="custom-div">检测项目</div>
        </span>
        <el-table
          height="52vh"
          v-loading="loading"
          :data="billItemResultList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column label="项目编码" align="center" prop="itemCode" />
          <el-table-column label="项目名称" align="center" prop="itemName" />
          <el-table-column label="仪器" align="center" prop="instrumentName" />
          <el-table-column
            label="检验方法"
            align="center"
            prop="inspectionMethod"
          />
          <el-table-column label="样品数1111" align="center" prop="sampleNum" />
          <el-table-column
            label="分析方法"
            align="center"
            prop="analysisMethod"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.analysis_method"
                :value="scope.row.analysisMethod"
              />
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center" prop="unit" />
          <el-table-column label="不良数" align="center" prop="qtyDefect" />

          <el-table-column label="测量结果" align="center" prop="judgeResult">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.judge_result"
                :value="scope.row.judgeResult"
              />
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                v-if="form.status == 'update'"
                @click="onInspect(scope.row, scope.$index)"
                >检测</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getBillItemList"
        /> -->
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="质检结果"
              prop="inspResult"
              style="width: 240px"
            >
              <el-select
                v-model="form.inspResult"
                placeholder="请选择质检结果"
                style="width: 240px"
                disabled
              >
                <el-option
                  v-for="dict in dict.type.insp_result"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注" prop="remark" style="width: 700px">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8"> </el-col>
        </el-row>
      </el-form>

      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="formItem" :model="formItem" :rules="rules">
          <el-table
            height="50vh"
            v-loading="loading"
            :data="formItem.billitemList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column label="下限值" align="center" prop="lowerLimit" />
            <el-table-column label="下限值判断" align="center" prop="lowerType">
              <template slot-scope="scope">
                <el-tag type="success">{{
                  handlelowerType(scope.row.lowerType)
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="测量值" align="center" prop="measuredValue">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'billitemList.' + scope.$index + '.measuredValue'"
                  :rules="rules.measuredValue"
                >
                  <el-input
                    @blur="blurMeasuredValue(scope.row)"
                    v-model="scope.row.measuredValue"
                    placeholder="请输入数量"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="上限值" align="center" prop="upperLimit" />
            <el-table-column label="上限值判断" align="center" prop="upperType"
              ><template slot-scope="scope">
                <el-tag type="success">{{
                  handleupperType(scope.row.upperType)
                }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="单位" align="center" prop="itemUnit" />
            <!-- <el-table-column
              label="是否合格"
              align="center"
              prop="qualitativeResults"
            /> -->
            <el-table-column label="判断结果" align="center" prop="judgeResult">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.judge_result"
                  :value="scope.row.judgeResult"
                />
              </template>
            </el-table-column>
          </el-table>

          <el-form-item label="不良数" prop="qtyDefect" style="width: 240px">
            <el-input
              v-model="formItem.qtyDefect"
              placeholder="请输入不良数"
              disabled
            />
          </el-form-item>
          <el-form-item label="不良现象" prop="defect" style="width: 500px">
            <el-input
              type="textarea"
              v-model="formItem.defect"
              placeholder="请输入不良现象"
            />
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
// import { listBill, getBill, delBill, addQuality } from "@/api/quality/bill";

import {
  listOqcbill,
  getOqcbill,
  delOqcbill,
  addQuality,
  addOqcbill,
  updateOqcbill,
} from "@/api/quality/oqcbill";

// import {
//   listBillitem,
//   getBillitem,
//   delBillitem,
//   addBillitem,
//   updateBillitem,
// } from "@/api/quality/billitem";

import { listOqcitem } from "@/api/quality/oqcitem";

// import {
//   listResult,
//   listByPart,
//   getResult,
//   delResult,
//   addResult,
//   updateResult,
// } from "@/api/quality/result";

import {
  listOqcresult,
  listByPart,
  getResult,
  delResult,
  addResult,
  updateResult,
} from "@/api/quality/oqcresult";

export default {
  name: "BillQuality",
  dicts: [
    "bill_status",
    "iqc_insp_config_strictness",
    "insp_result",
    "inspection_from",
    "inspection_from",
    "insp_type",
    "analysis_method",
    "judge_result",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //检测的行索引
      tableIndex: null,
      // oqc质检单据表格数据
      billItemResultList: [],
      //检测结果list
      billitemNumList: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
        oqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        comId: null,
      },
      // 表单参数
      form: {
        billItemResultList: [],
      },
      formItem: {
        //检测list
        billitemList: [],
      },
      // 表单校验
      rules: {
        measuredValue: [
          { required: true, message: "测量值不能为空", trigger: "blur" },
        ],
        inspResult: [
          { required: true, message: "质检结果不能为空", trigger: "blur" },
        ],
      },
    };
  },
  beforeRouteEnter(to, from, next) {
    if (to.query.title) {
      to.meta.title = to.query.title;
    }
    next();
  },
  created() {
    this.form = this.$route.query;
    this.queryParams.materialId = this.form.materialId;
    this.queryParams.batchNum = this.form.qty;
    this.queryParams.billId = this.form.id;
    this.getBillItemList();
  },
  methods: {
    isNotNull(data) {
      if (data === "" || data === undefined || data === null) return true;
      return false;
    },
    //测量值之后判断是否合格
    blurMeasuredValue(row) {
      let val = row.measuredValue * 1;
      console.log(row, val);
      //1、如果 上限值 和 下限值  ==null
      if (this.isNotNull(row.uppeLimit) && this.isNotNull(row.lowerLimit)) {
        console.log(row, "1");
        if (val == undefined) {
          row.judgeResult = null;
        } else {
          row.judgeResult = "[23]";
        }
        row.measuredValue = val.toFixed(3);
        this.onBlur(row);
        return;
      }
      //2、如果 上限值 ==null时
      if (this.isNotNull(row.uppeLimit) && !this.isNotNull(row.lowerLimit)) {
        console.log(row, "2");
        if (val == undefined) {
          row.judgeResult = null;
        } else {
          //否则将输入值和 下限值 按B判断符号类型 进行比较
          if (row.lowerType == "6") {
            //无
            if (val < row.lowerLimit) {
              row.judgeResult = "[24]";
            } else {
              row.judgeResult = "[23]";
            }
          } else if (row.lowerType == "1") {
            //大于
            if (val > row.lowerLimit) {
              row.judgeResult = "[23]";
            } else {
              row.judgeResult = "[24]";
            }
          } else if (row.lowerType == "2") {
            //大于等于
            if (val >= row.lowerLimit) {
              row.judgeResult = "[23]";
            } else {
              row.judgeResult = "[24]";
            }
          } else {
            //等于=
            console.log(val, row.lowerLimit, "row.lowerLimit");
            if (val == row.lowerLimit) {
              row.judgeResult = "[23]";
            } else {
              row.judgeResult = "[24]";
            }
          }
        }
        row.measuredValue = val.toFixed(3);
        this.onBlur(row);
        return;
      }
      //3、如果 下限值 ==null时
      if (this.isNotNull(row.lowerLimit) && !this.isNotNull(row.uppeLimit)) {
        console.log(row, "3");
        if (val == undefined) {
          row.judgeResult = null;
        } else {
          //否则将输入值和 上限值 按A判断符号类型 进行比较
          if (row.uppeType == "6") {
            //无
            if (val > row.uppeLimit) {
              row.judgeResult = "[24]";
            } else {
              row.judgeResult = "[23]";
            }
          } else if (row.uppeType == "4") {
            //小于
            if (val < row.uppeLimit) {
              row.judgeResult = "[23]";
            } else {
              row.judgeResult = "[24]";
            }
          } else if (row.uppeType == "5") {
            //小于等于
            if (val <= row.uppeLimit) {
              row.judgeResult = "[23]";
            } else {
              row.judgeResult = "[24]";
            }
          } else {
            //等于=
            if ((val = row.uppeLimit)) {
              row.judgeResult = "[23]";
            } else {
              row.judgeResult = "[24]";
            }
          }
        }
        row.measuredValue = val.toFixed(3);
        this.onBlur(row);
        return;
      }
      //4、如果 上限值 和 下限值  !=null
      if (!this.isNotNull(row.uppeLimit) && !this.isNotNull(row.lowerLimit)) {
        console.log(row, "4");
        console.log(row.uppeLimit, "--", row.lowerLimit);

        console.log(row.uppeType, "--", row.lowerType);
        if (val == undefined) {
          row.judgeResult = null;
        } else {
          //否则将输入值和 下限值 按B判断符号类型 进行比较
          //否则将输入值和 上限值 按A判断符号类型 进行比较
          //拿上限值进行判断
          if (row.uppeType == "3") {
            //=
            if (row.lowerType == "1") {
              //>
              //lower下限值>输入值=upper上限值
              if (val == row.uppeLimit && val > row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "2") {
              //>=
              //lower下限值>=输入值=upper上限值
              if (val == row.uppeLimit && val >= row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "3") {
              //=
              //lower下限值=输入值=upper上限值
              if (val == row.uppeLimit && val == row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else {
              //无
              //lower下限值(无)输入值=upper上限值
              if (val == row.uppeLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            }
          } else if (row.uppeType == "4") {
            //<
            if (row.lowerType == "1") {
              //>
              //lower下限值>输入值<upper上限值
              if (val < row.uppeLimit && val > row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "2") {
              //>=
              //lower下限值>=输入值<upper上限值
              if (val < row.uppeLimit && val >= row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "3") {
              //=
              //lower下限值=输入值<upper上限值
              if (val < row.uppeLimit && val == row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else {
              //无
              //lower下限值(无)输入值<upper上限值
              if (val < row.uppeLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            }
          } else if (row.uppeType == "5") {
            //<=
            if (row.lowerType == "1") {
              //>
              //lower下限值>输入值<=upper上限值
              if (val <= row.uppeLimit && val > row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "2") {
              //>=
              //lower下限值>=输入值<=upper上限值
              if (val <= row.uppeLimit && val >= row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "3") {
              //=
              //lower下限值=输入值<=upper上限值
              if (val <= row.uppeLimit && val == row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else {
              //无
              //lower下限值(无)输入值<=upper上限值
              if (val <= row.uppeLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            }
          } else {
            //无
            if (row.lowerType == "1") {
              //>
              //lower下限值>输入值(无)upper上限值
              if (val > row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "2") {
              //>=
              //lower下限值>=输入值(无)upper上限值
              if (val >= row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else if (row.lowerType == "3") {
              //=
              //lower下限值=输入值(无)upper上限值
              if (val == row.lowerLimit) {
                row.judgeResult = "[23]";
              } else {
                row.judgeResult = "[24]";
              }
            } else {
              //无
              //lower下限值(无)输入值(无)upper上限值
              row.judgeResult = null;
            }
          }
        }
        row.measuredValue = val.toFixed(3);
        this.onBlur(row);
        return;
      }
    },
    onBlur(row) {
      const dataList = this.formItem.billitemList || [];
      console.log(dataList, "dataList");
      let defect_qty = this.formItem.qtyDefect;
      console.log("onBlur");
      if (row.judgeResult == "[24]") {
        let temp = defect_qty;
        if (temp + 1 < this.formItem.sampleNum) {
          console.log("if");
          let length = 0;
          for (let j in dataList) {
            if (dataList[j].judgeResult == "[24]") {
              length++;
            }
          }
          console.log(length, "length1");
          this.formItem.qtyDefect = length;
        } else {
          console.log("else");
          this.formItem.qtyDefect = this.formItem.sampleNum;
        }
      } else {
        let length = 0;
        for (let j in dataList) {
          if (dataList[j].judgeResult == "[24]") {
            length++;
          }
        }
        console.log(length, "length2");
        this.formItem.qtyDefect = length;
      }
      console.log(
        this.formItem,
        this.formItem.billitemList,
        "this.formItem.billitemList"
      );
    },
    handlelowerType(val) {
      let text = null;
      if (val == "1") {
        text = ">";
      } else if (val == "2") {
        text = ">=";
      } else if (val == "3") {
        text = "=";
      } else {
        text = "无";
      }
      return text;
    },
    handleupperType(val) {
      let text = null;
      if (val == "3") {
        text = "=";
      } else if (val == "4") {
        text = "<";
      } else if (val == "5") {
        text = "<=";
      } else {
        text = "无";
      }
      return text;
    },
    /** 查询oqc检检测项目列表 */
    getBillItemList() {
      this.loading = true;
      listOqcresult(this.queryParams).then((response) => {
        if (response.rows.length == 0) {
          //查询检验项目
          listByPart(this.queryParams).then((res) => {
            if (res.rows.length == 0) {
              this.$message({
                message: "请先配置此物料的检验项目",
                type: "warning",
              });
            }
            this.billItemResultList = res.rows;
            this.total = res.total;
            this.loading = false;
          });
        } else {
          for (let i = 0; i < response.rows.length; i++) {
            const params = {
              inspBillId: this.form.id,
              itemCode: response.rows[i].itemCode,
              pageNum: 1,
              pageSize: 10000,
            };
            listOqcitem(params).then((res) => {
              if (res.rows.length > 0) {
                response.rows[i].billitemList = res.rows;
              }
            });
          }
          this.billItemResultList = response.rows;
          console.log("test----------->");
          console.log(this.billItemResultList, "this.billItemResultList");

          this.total = response.total;
          this.loading = false;
        }
      });
    },
    goBack() {
      // 关闭当前标签页并返回上个页面
      const obj = {
        path: "/quality/oqcbill",
        query: { t: Date.now() },
      };
      this.$tab.closeOpenPage(obj);
    },
    workArea() {
      this.form.flagStatus = false;
    },
    firstSubmit() {
      this.form.billItemResultList = this.billItemResultList;
      if (this.form.billItemResultList.length == 0) {
        this.$message({
          message: "请配置检验项目",
          type: "warning",
        });
        return;
      }
      for (let i = 0; i < this.form.billItemResultList.length; i++) {
        const item = this.form.billItemResultList[i];
        if (!item.judgeResult) {
          this.$message({
            message: "请先检验完所有项目再进行提交",
            type: "warning",
          });
          return;
        }
      }
      this.$refs["form"].validate((valid) => {
        console.log(this.form, "form");
        if (valid) {
          this.form.flagStatus = true;
          addQuality(this.form).then((response) => {
            this.$modal.msgSuccess("提交成功");
            this.goBack();
          });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        oqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        qtyDefect: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getBillItemList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加oqc质检单据";
    },
    /** 修改按钮操作 */
    onInspect(row, index) {
      console.log(this.billItemResultList, "this.billItemResultLis");
      this.open = true;
      this.tableIndex = index;
      this.title = "测量项目";
      const params = {
        inspBillId: this.form.id,
        itemCode: row.itemCode,
        pageNum: 1,
        pageSize: 10000,
      };
      console.log("params----------->", params);
      if (!row.billitemList) {
        listOqcitem(params).then((response) => {
          if (response.rows.length > 0) {
            this.formItem.billitemList = response.rows;
            console.log(
              this.formItem.billitemList,
              "this.formItem.billitemList"
            );
          } else {
            let data = [];
            for (let i = 0; i < row.sampleNum; i++) {
              data.push({
                itemId: i + 1,
                measuredValue: undefined,
                judgeResult: null,
                itemCode: row.itemCode,
                itemName: row.itemName,
                // upper_number: row.uppeLimit,
                // lower_number: row.lowerLimit,
                upperLimit: row.upperLimit,
                lowerLimit: row.lowerLimit,
                lowerType: row.lowerType,
                upperType: row.upperType,
                unit: row.unit,
                technicalStandard: row.technicalStandard,
              });
            }
            console.log("response.rows.length < 0");
            console.log(data, "data");

            this.formItem.billitemList = data;
          }
        });
      } else {
        this.formItem.billitemList = row.billitemList;
      }
      this.formItem.sampleNum = row.sampleNum;
      this.formItem.qtyDefect = row.qtyDefect || 0;
      this.formItem.defect = row.defect || "";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["formItem"].validate((valid) => {
        if (valid) {
          this.billItemResultList[this.tableIndex].billitemList =
            this.formItem.billitemList;
          //不良数
          this.billItemResultList[this.tableIndex].qtyDefect =
            this.formItem.qtyDefect;
          //不良现象
          this.billItemResultList[this.tableIndex].defect =
            this.formItem.defect;
          if (this.formItem.qtyDefect > 0) {
            //不合格
            this.billItemResultList[this.tableIndex].judgeResult = "[24]";
            this.form.inspResult = "unqualified";
          } else {
            //合格
            this.billItemResultList[this.tableIndex].judgeResult = "[23]";
            let flag = true;
            //判断表格中是否有不合格的
            this.billItemResultList.map((ele) => {
              if (ele.judgeResult === "[24]") {
                // 当有不合格时，flag 为 false
                flag = false;
              }
              if (!flag) {
                this.form.inspResult = "unqualified";
              } else {
                this.form.inspResult = "qualified";
              }
            });
          }
          this.open = false;
          console.log(this.billItemResultList, "this.billItemResultLis");
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除oqc质检单据编号为"' + ids + '"的数据项？')
        .then(function () {
          return delOqcbill(ids);
        })
        .then(() => {
          this.getBillItemList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/bill/export",
        {
          ...this.queryParams,
        },
        `oqc质检单据_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>