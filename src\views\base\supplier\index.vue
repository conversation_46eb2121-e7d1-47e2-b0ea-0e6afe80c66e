<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="88px">
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input v-model="queryParams.supplierCode" placeholder="请输入供应商编码" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:supplier:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:supplier:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:supplier:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:supplier:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="supplierList" @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="id" align="center" prop="id" /> -->
        <el-table-column label="供应商编码" align="center" prop="supplierCode" />
        <el-table-column label="供应商名称" align="center" prop="supplierName" />
        <!-- <el-table-column
          label="供应商类型"
          align="center"
          prop="supplierType"
        /> -->
        <el-table-column label="联系人" align="center" prop="contacts" />
        <el-table-column label="手机号" align="center" prop="phone" />
        <el-table-column label="供应商邮箱" align="center" prop="supplierEmail" />
        <el-table-column label="供应商地址" align="center" prop="supplierAddress" />
        <!-- <el-table-column label="状态" align="center" prop="status" /> -->
        <el-table-column label="备注" align="center" prop="remark" />
        <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:supplier:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:supplier:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改 供应商管理对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="供应商信息" name="1">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="供应商编码" prop="supplierCode" style="width: 240px">
                    <el-input v-model="form.supplierCode" placeholder="请输入供应商编码" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="供应商编码" prop="supplierCode" style="width: 240px">
                    <el-input v-model="form.supplierCode" placeholder="请输入供应商编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="供应商名称" prop="supplierName" style="width: 240px">
                    <el-input v-model="form.supplierName" placeholder="请输入供应商名称" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系人" prop="contacts" style="width: 240px">
                    <el-input v-model="form.contacts" placeholder="请输入联系人" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号" prop="phone" style="width: 240px">
                    <el-input v-model="form.phone" placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="供应商邮箱" prop="supplierEmail" style="width: 240px">
                    <el-input v-model="form.supplierEmail" placeholder="请输入供应商邮箱" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="供应商地址" prop="supplierAddress" style="width: 240px">
                    <el-input v-model="form.supplierAddress" placeholder="请输入供应商地址" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="备注" prop="remark" style="width: 720px">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-collapse-item>
          </el-collapse>


          <!-- <el-form-item label="删除标志" prop="delFlag">
            <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
          </el-form-item> -->
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listSupplier,
  getSupplier,
  delSupplier,
  addSupplier,
  updateSupplier,
} from "@/api/system/supplier";

export default {
  name: "Supplier",
  data() {
    return {
      activeNames: ["1"],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //  供应商管理表格数据
      supplierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierCode: null,
        supplierName: null,
        supplierType: null,
        contacts: null,
        phone: null,
        supplierEmail: null,
        supplierAddress: null,
        status: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        supplierCode: [
          { required: true, trigger: "blur", message: "供应商编码不能为空" },
        ],
        supplierName: [
          { required: true, trigger: "blur", message: "供应商名称不能为空" },
        ],
        contacts: [
          { required: true, trigger: "blur", message: "联系人不能为空" },
        ],
        supplierEmail: [
          { required: true, trigger: "blur", message: "供应商邮箱不能为空" },
        ],
        supplierAddress: [
          { required: true, trigger: "blur", message: "供应商地址不能为空" },
        ],
        phone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询 供应商管理列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      listSupplier(this.queryParams).then((response) => {
        this.supplierList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        supplierCode: null,
        supplierName: null,
        supplierType: null,
        contacts: null,
        phone: null,
        supplierEmail: null,
        supplierAddress: null,
        status: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加 供应商管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSupplier(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改 供应商管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSupplier(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupplier(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除 供应商管理编号为"' + row.supplierCode + '"的数据项？'
        )
        .then(function () {
          return delSupplier(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/supplier/export",
        {
          ...this.queryParams,
        },
        `supplier_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>