<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="css/hiprint.css" rel="stylesheet" />
    <link href="css/print-lock.css" rel="stylesheet" />

    <link href="content/bootstrap.min.css" rel="stylesheet">
    <script src="https://libs.baidu.com/jquery/1.11.3/jquery.min.js"></script>
    <script src="content/bootstrap.min.js"></script>

    <style>
        .hinnn-layout,
        .hinnn-layout * {
            box-sizing: border-box;
        }


        .hinnn-layout {
            display: flex;
            flex: auto;
            flex-direction: column;

        }

        .hinnn-layout.hinnn-layout-has-sider {
            flex-direction: row;
        }

        .hinnn-layout-sider {
            display: flex;
            flex-direction: row;
            position: relative;
        }

        .hinnn-layout-content {
            flex: auto;
        }

        .hinnn-header {
            position: relative;

            z-index: 1030;
            display: block;
        }


        .wrapper {
            min-height: 100%;
        }

        .height-100-per {
            height: 100%;
        }
    </style>
</head>

<body>
    <layout class="layout hinnn-layout hinnn-layout-has-sider height-100-per" style="background:#fff;">


        <content class="hinnn-layout-content" style="border-left:1px solid #e8e8e8;">
            <div class="container-fluid height-100-per print-content">


                <div class="row">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-3 col-md-2" style="padding-right:0px;">

                                <div class="rect-printElement-types hiprintEpContainer">
                                    <ul class="hiprint-printElement-type">

                                        <li>
                                            <span class="title"><code>拖拽列表</code></span>
                                            <ul>
                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.text" style="">

                                                        <span class="glyphicon glyphicon-text-width"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">文本</span>
                                                    </a>
                                                </li>

                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.image" style="">
                                                        <span class="glyphicon glyphicon-picture"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">图片</span>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.longText">
                                                        <span class="glyphicon glyphicon-subscript"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">长文</span>


                                                    </a>
                                                </li>

                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.tableCustom" style="">
                                                        <span class="glyphicon glyphicon-th" aria-hidden="true"></span>
                                                        <span class="glyphicon-class">表格</span>
                                                    </a>
                                                </li>

                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.html">
                                                        <span class="glyphicon glyphicon-header"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">html</span>
                                                    </a>
                                                </li>

                                            </ul>
                                        </li>
                                        <li>
                                            <span class="title">辅助</span>
                                            <ul>
                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.hline" style="">

                                                        <span class="glyphicon glyphicon-resize-horizontal"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">横线</span>
                                                    </a>
                                                </li>

                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.vline" style="">
                                                        <span class="glyphicon glyphicon-resize-vertical"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">竖线</span>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.rect">
                                                        <span class="glyphicon glyphicon-unchecked"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">矩形</span>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="ep-draggable-item" tid="testModule.oval">
                                                        <span class="glyphicon glyphicon-record"
                                                            aria-hidden="true"></span>
                                                        <span class="glyphicon-class">椭圆</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>

                            </div>
                            <div class="col-sm-9 col-md-10">
                                <div class="hiprint-toolbar" style="margin-top:15px;">
                                    <ul>
                                        <li><a class="hiprint-toolbar-item" onclick="setPaper('A3')">A3</a></li>
                                        <li><a class="hiprint-toolbar-item" onclick="setPaper('A4')">A4</a></li>
                                        <li><a class="hiprint-toolbar-item" onclick="setPaper('A5')">A5</a></li>
                                        <li><a class="hiprint-toolbar-item" onclick="setPaper('B3')">B3</a></li>
                                        <li><a class="hiprint-toolbar-item" onclick="setPaper('B4')">B4</a></li>
                                        <li><a class="hiprint-toolbar-item" onclick="setPaper('B5')">B5</a></li>

                                        <li><a class="hiprint-toolbar-item"><input type="text" id="customWidth"
                                                    style="width: 50px;height: 19px;border: 0px;"
                                                    placeholder="宽/mm" /></a></li>
                                        <li><a class="hiprint-toolbar-item"><input type="text" id="customHeight"
                                                    style="width: 50px;height: 19px;border: 0px;"
                                                    placeholder="高/mm" /></a></li>

                                        <li><a class="hiprint-toolbar-item"
                                                onclick="setPaper($('#customWidth').val(),$('#customHeight').val())">自定义</a>
                                        </li>
                                        <li><a class="hiprint-toolbar-item" onclick="rotatePaper()">旋转</a></li>
                                        <li><a class="hiprint-toolbar-item" onclick="clearTemplate()">清空</a></li>

                                        <li>
                                            <a class="btn hiprint-toolbar-item " style="color: #fff;
    background-color: #d9534f;
    border-color: #d43f3a;" id="A4_preview">快速预览</a>
                                        </li>
                                        <li>
                                            <a id="A4_directPrint" class="btn hiprint-toolbar-item " style="color: #fff;
    background-color: #d9534f;
    border-color: #d43f3a;">打印</a>
                                        </li>
                                        <li>
                                            <a id="A4_submitPrint" class="btn hiprint-toolbar-item " style="color: #fff;
    background-color: #3b89c1;
    border-color: #3b89c1;">保存</a>
                                        </li>

                                    </ul>
                                    <div style="clear:both;"></div>
                                </div>
                                <div id="hiprint-printTemplate" class="hiprint-printTemplate" style="margin-top:20px;">

                                </div>
                                <div style="padding-top:15px;">
                                    <button type="button" class="btn btn-primary"
                                        id="A4_getJson_toTextarea">生成json到textarea</button>
                                </div>
                                <div class="hinnn-callout hinnn-callout-danger">

                                    <p><code>可视化</code>结果以Json的形式存在，用户可以<code>编辑Json</code>实现特殊化操作，如：数据<code>formatter</code>，<code>文本变色</code>,单元格<code>改变背景</code>等。具体请参考文档。
                                    </p>
                                </div>
                                <textarea class="form-control" rows="10" id="A4_textarea_json"></textarea>
                                <div style="padding:15px 0;">
                                    <button type="button" class="btn btn-danger"
                                        id="A4_getHtml_toTextarea">生成html到textarea</button>
                                </div>

                                <textarea class="form-control" rows="10" id="A4_textarea_html">

                            </textarea>

                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </content>
        <sider class="hinnn-layout-sider" style="">
            <div class="container height-100-per" style="width:250px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div id="PrintElementOptionSetting" style="margin-top:10px;"></div>
                    </div>
                </div>
            </div>


        </sider>
    </layout>


    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document" style="width: 825px;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">打印预览</h4>
                </div>

                <div class="modal-body">
                    <button type="button" class="btn btn-danger" id="A4_printByHtml">打印</button>
                    <div class="prevViewDiv"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>

                </div>
            </div>
        </div>
    </div>


    <!--[[ 测试专用  单独使用无需引入  -->
    <script src="custom_test/custom-etype-provider.js"></script>
    <script src="custom_test/custom-print-json.js"></script>
    <script src="custom_test/print-data.js"></script>
    <!--测试专用  单独使用无需引入 ]]  -->
    <!--单独使用无需引入  -->
    <script src="polyfill.min.js"></script>
    <script src="plugins/jquery.minicolors.min.js"></script>
    <script src="plugins/JsBarcode.all.min.js"></script>
    <script src="plugins/qrcode.js"></script>
    <script src="hiprint.bundle.js"></script>
    <script src="plugins/jquery.hiwprint.js"></script>


    <script>
        var hiprintTemplate;
        $(document).ready(function () {

            //初始化打印插件
            hiprint.init({
                providers: [new customElementTypeProvider()]
            });

            // hiprint.PrintElementTypeManager.build('.hiprintEpContainer', 'testModule');
            //设置左侧拖拽事件
            hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'));
            const nowPrintTemplate = JSON.parse(sessionStorage.getItem("nowPrintTemplate"))
            console.log(nowPrintTemplate,"nowPrintTemplate")
            const json = {
                panels: [
                    {
                        index: 0,
                        height: 297,
                        width: 210,
                        paperHeader: 49.5,
                        paperFooter: 780,
                        printElements: [
                            {
                                options: { left: 105, top: 111, height: 345, width: 442.5 },
                                printElementType: { type: "rect" },
                            },
                            {
                                options: { left: 303, top: 114, height: 339, width: 9 },
                                printElementType: { type: "vline" },
                            },
                            {
                                options: {
                                    left: 319.5,
                                    top: 159,
                                    height: 9.75,
                                    width: 120,
                                    field: "materialCode",
                                    fontSize: 19.5,
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 162,
                                    top: 159,
                                    height: 9.75,
                                    width: 120,
                                    title: "物料编号：",
                                    fontSize: 20.25,
                                    fontWeight: "bold",
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 319.5,
                                    top: 198,
                                    height: 9.75,
                                    width: 120,
                                    field: "materialName",
                                    fontSize: 19.5,
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 162,
                                    top: 199.5,
                                    height: 9.75,
                                    width: 120,
                                    title: "物料名称：",
                                    fontSize: 20.25,
                                    fontWeight: "bold",
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 319.5,
                                    top: 247.5,
                                    height: 9.75,
                                    width: 120,
                                    field: "dateCode",
                                    fontSize: 19.5,
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 162,
                                    top: 247.5,
                                    height: 9.75,
                                    width: 120,
                                    title: "生产日期",
                                    fontSize: 20.25,
                                    fontWeight: "bold",
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 318,
                                    top: 289.5,
                                    height: 9.75,
                                    width: 120,
                                    field: "batchNo",
                                    fontSize: 19.5,
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 163.5,
                                    top: 291,
                                    height: 9.75,
                                    width: 120,
                                    title: "批次：",
                                    fontSize: 20.25,
                                    fontWeight: "bold",
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 318,
                                    top: 336,
                                    height: 9.75,
                                    width: 120,
                                    field: "boxNo",
                                    fontSize: 19.5,
                                },
                                printElementType: { type: "text" },
                            },
                            {
                                options: {
                                    left: 162,
                                    top: 336,
                                    height: 9.75,
                                    width: 120,
                                    title: "箱号：",
                                    fontSize: 20.25,
                                    fontWeight: "bold",
                                },
                                printElementType: { type: "text" },
                            },
                        ],
                        paperNumberLeft: 565.5,
                        paperNumberTop: 819,
                    },
                ],
            }
            console.log(JSON.stringify(json), "json")
            // const jsonString = JSON.stringify(nowPrintTemplate.printJson);
            // console.log(jsonString,"jsonString")
            const json888 = JSON.parse(nowPrintTemplate.printJson)
            console.log(json888, "nowPrintTemplate")
            // console.log(JSON.parse(json888), "JSON.parse(json888)")
            hiprintTemplate = new hiprint.PrintTemplate({
                template: json888,
                settingContainer: '#PrintElementOptionSetting',
                paginationContainer: '.hiprint-printPagination'
            });
            //打印设计
            hiprintTemplate.design('#hiprint-printTemplate');

            $('#A4_preview').click(function () {
                $('#myModal .modal-body .prevViewDiv').html(hiprintTemplate.getHtml(printData))
                $('#myModal').modal('show')
            });
            $('#A4_directPrint').click(function () {
                hiprintTemplate.print(printData);
            });
            $('#A4_submitPrint').click(function () {
                let data = {
                    printJson: JSON.stringify(hiprintTemplate.getJson()),
                    type: 'jsonData'
                }
                // 传参
                console.log(data, "000")
                window.parent.postMessage(data, '*')
                console.log(JSON.stringify(hiprintTemplate.getJson()), "0000jjjj")
                // sessionStorage.setItem(
                //     "printJson",
                //     JSON.stringify(hiprintTemplate.getJson())

                // );
                // this.$emit("submit",null)
                // hiprintTemplate.print(printData);
            });
            $('#A4_printByHtml').click(function () {
                hiprintTemplate.printByHtml($('#myModal .modal-body .prevViewDiv'));
            })
            $('#A4_getJson_toTextarea').click(function () {
                $('#A4_textarea_json').html(JSON.stringify(hiprintTemplate.getJson()))
            })
            $('#A4_getHtml_toTextarea').click(function () {
                $('#A4_textarea_html').val(hiprintTemplate.getHtml(printData)[0].outerHTML)
            })
        });

        //调整纸张
        var setPaper = function (paperTypeOrWidth, height) {
            hiprintTemplate.setPaper(paperTypeOrWidth, height);
        }

        //旋转
        var rotatePaper = function () {
            hiprintTemplate.rotatePaper();
        }
        var clearTemplate = function () {
            hiprintTemplate.clear();
        }

    </script>

    <script>
        $(document).ready(function () {
            //设置左侧拖拽事件
            var hiprintTemplate_bill = new hiprint.PrintTemplate({
                template: JSON.parse($('#textarea_bill').val()),
                settingContainer: '#PrintElementOptionSetting'
            });
            //打印设计
            hiprintTemplate_bill.design('#hiprint-printTemplate_bill');

            $('#bill_preview').click(function () {
                $('#myModal .modal-body .prevViewDiv').html(hiprintTemplate_bill.getHtml(printData))
                $('#myModal').modal('show')
            });
            $('#bill_print').click(function () {
                hiprintTemplate_bill.print(printData);
            });
        });


    </script>

    <script type="text/javascript">
        $(document).ready(function () {
            $('#barcode_button_preview').click(function () {
                var barCodehiprintTemplate = new hiprint.PrintTemplate({ template: JSON.parse($('#textarea_barcode').val()) });

                var $html = barCodehiprintTemplate.getHtml([{ name: '黄山', barcode: '13234567' }, { name: '黄波', barcode: '1224567' }, { name: '黄磊', barcode: '1234567' }, { name: '黄磊', barcode: '1234567' }, { name: '古丽娜', barcode: '7654321' }])

                $('#myModal .modal-body .prevViewDiv').html($html)
                $('#myModal').modal('show')
            });

            $('#barcode_button_print').click(function () {
                var barCodehiprintTemplate = new hiprint.PrintTemplate({ template: JSON.parse($('#textarea_barcode').val()) });

                var $html = barCodehiprintTemplate.getHtml([{ name: '黄山', barcode: '13234567' }, { name: '黄波', barcode: '1224567' }, { name: '黄磊', barcode: '1234567' }, { name: '黄磊', barcode: '1234567' }, { name: '古丽娜', barcode: '7654321' }])
                barCodehiprintTemplate.printByHtml($html);

            });
        })
    </script>

</body>

</html>